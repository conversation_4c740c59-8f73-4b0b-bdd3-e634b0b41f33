<?php
/* Smarty version 5.5.1, created on 2025-07-10 08:32:24
  from 'file:menu.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.5.1',
  'unifunc' => 'content_686f5e78569ce5_93122659',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'db8babcd242eda836ed67cb2efcc8c397ec51d62' => 
    array (
      0 => 'menu.tpl',
      1 => 1752129133,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
))) {
function content_686f5e78569ce5_93122659 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates';
?><nav class="navbar navbar-default navbar-fixed-top">
	<div class="container-fluid">
		<!-- Brand and toggle get grouped for better mobile display -->
		<div class="navbar-header">
			<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
				<span class="sr-only">Toggle navigation</span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
			</button>
			<a class="navbar-brand" href="/" >			
				<img src="img/mark.png" width="36" class="pull-left"/>							
			</a>
		</div>

		<!-- Collect the nav links, forms, and other content for toggling -->
		<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
			<ul class="nav navbar-nav">
				<li class="branch_code">
					<a href="#" <?php $_block_repeat=true;
if (!$_smarty_tpl->getSmarty()->getBlockHandler('access')) {
throw new \Smarty\Exception('block tag \'access\' not callable or registered');
}

echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"CROSS_BRANCH"), null, $_smarty_tpl, $_block_repeat);
while ($_block_repeat) {
  ob_start();
?> id="change_branch" <?php $_block_repeat=false;
echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"CROSS_BRANCH"), ob_get_clean(), $_smarty_tpl, $_block_repeat);
}
?>>
						<?php echo $_smarty_tpl->getValue('currentBranch')['code'];?>

					</a>
				</li>
				<li><a href="<?php echo $_smarty_tpl->getSmarty()->getFunctionHandler('route')->handle(array('name'=>"home"), $_smarty_tpl);?>
">Dashboard</a></li>
				<li><a href="<?php echo $_smarty_tpl->getSmarty()->getFunctionHandler('route')->handle(array('name'=>"logout"), $_smarty_tpl);?>
">Logout</a></li>
			</ul>
			<ul class="nav navbar-nav navbar-right">
				
				
			</ul>
		</div><!-- /.navbar-collapse -->
	</div><!-- /.container-fluid -->
</nav>
<?php }
}
