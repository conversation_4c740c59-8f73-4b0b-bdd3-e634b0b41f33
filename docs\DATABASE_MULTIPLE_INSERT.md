# Database Multiple Row Insert

This document explains the multiple row insertion functionality added to the Database class.

## Overview

The Database class now supports efficient insertion of multiple rows of data using two new methods:
- `insertMany()` - Insert multiple rows in a single query
- `insertManyWithTransaction()` - Insert multiple rows with transaction support and batching

## Methods

### 1. insertMany(array $data): bool

Inserts multiple rows using a single SQL statement for better performance.

**Parameters:**
- `$data` (array): Array of associative arrays, each representing a row

**Returns:**
- `bool`: True if all rows were inserted successfully

**Example:**
```php
$data = [
    ['name' => '<PERSON>', 'email' => '<EMAIL>', 'age' => 30],
    ['name' => '<PERSON>', 'email' => '<EMAIL>', 'age' => 25],
    ['name' => '<PERSON>', 'email' => '<EMAIL>', 'age' => 35]
];

$db = new Database();
$result = $db->table('users')->insertMany($data);
```

### 2. insertManyWithTransaction(array $data, int $batchSize = 1000): bool

Inserts multiple rows within a transaction with batching support for large datasets.

**Parameters:**
- `$data` (array): Array of associative arrays, each representing a row
- `$batchSize` (int): Number of rows to insert per batch (default: 1000)

**Returns:**
- `bool`: True if all rows were inserted successfully

**Example:**
```php
$largeData = []; // Array with thousands of rows
for ($i = 1; $i <= 5000; $i++) {
    $largeData[] = [
        'name' => "User {$i}",
        'email' => "user{$i}@example.com",
        'age' => rand(18, 65)
    ];
}

$db = new Database();
$result = $db->table('users')->insertManyWithTransaction($largeData, 1000);
```

## Requirements

### Data Structure
- All rows must have the same column structure
- Each row must be an associative array with column names as keys
- Data array cannot be empty

### Database
- Table must exist
- Column names in data must match actual table columns
- For transaction method: database must support transactions

## Error Handling

Both methods include comprehensive error handling:

```php
try {
    $db->table('users')->insertMany($data);
    echo "Insert successful!";
} catch (Exception $e) {
    echo "Insert failed: " . $e->getMessage();
}
```

### Common Exceptions

1. **Empty data array:**
   ```
   Data array cannot be empty for insertMany()
   ```

2. **Inconsistent column structure:**
   ```
   All rows must have the same column structure. Row 2 has different columns.
   ```

3. **Database errors:**
   ```
   Database error: Table 'users' doesn't exist
   ```

## Performance Comparison

### Single Insert Approach (Slow)
```php
// DON'T DO THIS for multiple rows
foreach ($data as $row) {
    $db->table('users')->insert($row);
}
```

### Batch Insert Approaches (Fast)
```php
// Good: Single query for multiple rows
$db->table('users')->insertMany($data);

// Best: Transaction with batching for large datasets
$db->table('users')->insertManyWithTransaction($data, 1000);
```

### Performance Benefits

- **Reduced Database Round Trips**: Single query vs multiple queries
- **Better Resource Utilization**: Less connection overhead
- **Faster Execution**: Bulk operations are optimized by database engines
- **Transaction Safety**: All-or-nothing approach with rollback support

## Debug Bar Integration

Both methods integrate with the PHP Debug Bar for monitoring:

- Query logging with execution details
- Data structure logging
- Error logging with detailed messages
- Transaction status tracking
- Batch progress monitoring

## Usage Examples

### Basic Multiple Insert
```php
use App\Core\Database;

$users = [
    ['name' => 'Alice', 'email' => '<EMAIL>', 'role' => 'admin'],
    ['name' => 'Bob', 'email' => '<EMAIL>', 'role' => 'user'],
    ['name' => 'Charlie', 'email' => '<EMAIL>', 'role' => 'user']
];

$db = new Database();
$success = $db->table('users')->insertMany($users);

if ($success) {
    echo "All users inserted successfully!";
}
```

### Large Dataset with Transaction
```php
use App\Core\Database;

// Generate large dataset
$products = [];
for ($i = 1; $i <= 10000; $i++) {
    $products[] = [
        'name' => "Product {$i}",
        'price' => rand(10, 1000),
        'category_id' => rand(1, 10),
        'created_at' => date('Y-m-d H:i:s')
    ];
}

$db = new Database();

try {
    // Insert in batches of 500 with transaction support
    $success = $db->table('products')->insertManyWithTransaction($products, 500);
    echo "All 10,000 products inserted successfully!";
} catch (Exception $e) {
    echo "Insert failed: " . $e->getMessage();
}
```

### With Connection Specification
```php
$db = new Database('secondary_db');
$result = $db->table('logs')->insertMany($logData);
```

## Best Practices

1. **Use appropriate batch sizes**: 500-1000 rows per batch for optimal performance
2. **Validate data structure**: Ensure all rows have consistent columns before insertion
3. **Use transactions for large datasets**: Provides rollback capability on failures
4. **Handle exceptions**: Always wrap in try-catch blocks
5. **Monitor with debug bar**: Use debug bar to monitor query performance
6. **Consider memory usage**: For very large datasets, consider processing in chunks

## Migration from Single Inserts

### Before (Inefficient)
```php
foreach ($users as $user) {
    $db->table('users')->insert($user);
}
```

### After (Efficient)
```php
$db->table('users')->insertMany($users);
```

### For Large Datasets
```php
$db->table('users')->insertManyWithTransaction($users, 1000);
```

## Troubleshooting

### Issue: "All rows must have the same column structure"
**Solution:** Ensure all array elements have identical keys
```php
// Wrong
$data = [
    ['name' => 'John', 'email' => '<EMAIL>'],
    ['name' => 'Jane', 'email' => '<EMAIL>', 'age' => 25] // Extra 'age' field
];

// Correct
$data = [
    ['name' => 'John', 'email' => '<EMAIL>', 'age' => null],
    ['name' => 'Jane', 'email' => '<EMAIL>', 'age' => 25]
];
```

### Issue: Memory exhaustion with large datasets
**Solution:** Use `insertManyWithTransaction()` with appropriate batch size
```php
// Instead of inserting 100,000 rows at once
$db->table('users')->insertManyWithTransaction($hugeData, 500);
```

### Issue: Transaction rollback
**Solution:** Check debug bar logs for specific error details and fix data issues

## Compatibility

- **PHP Version**: 7.4+
- **Database**: MySQL/MariaDB with PDO
- **Transaction Support**: Required for `insertManyWithTransaction()`
- **Debug Bar**: Optional but recommended for monitoring

## Methods Available

### Insert Methods
1. insert($data) - Single row insert
2. insertMany($data) - Multiple rows insert
3. insertManyWithTransaction($data, $batchSize) - Batch insert with transaction

### Replace Methods (MySQL REPLACE INTO)
4. replace($data) - Single row replace (upsert)
5. replaceMany($data) - Multiple rows replace (upsert)
6. replaceManyWithTransaction($data, $batchSize) - Batch replace with transaction

For detailed information about REPLACE INTO functionality, see [DATABASE_REPLACE_INTO.md](DATABASE_REPLACE_INTO.md).
