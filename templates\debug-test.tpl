{extends file="base.tpl"}

{block name="header"}
<style>
.debug-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}
.debug-section h3 {
    margin-top: 0;
    color: #333;
}
.debug-info {
    background-color: #e7f3ff;
    padding: 10px;
    border-radius: 3px;
    margin: 10px 0;
}
.debug-warning {
    background-color: #fff3cd;
    padding: 10px;
    border-radius: 3px;
    margin: 10px 0;
}
.debug-error {
    background-color: #f8d7da;
    padding: 10px;
    border-radius: 3px;
    margin: 10px 0;
}
</style>
{/block}

{block name="body"}
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1>{$title}</h1>
            <p class="lead">This page demonstrates PHP Debug Bar functionality in development mode.</p>
            
            {if $debug_enabled}
                <div class="alert alert-success">
                    <strong>Debug Mode Enabled!</strong> The debug bar should be visible at the bottom of this page.
                </div>
            {else}
                <div class="alert alert-warning">
                    <strong>Debug Mode Disabled!</strong> Enable debug mode in your .env file to see the debug bar.
                </div>
            {/if}
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="debug-section">
                <h3>Database Test</h3>
                <p>Testing database queries and debug bar SQL monitoring:</p>
                
                {if $users}
                    <div class="debug-info">
                        <strong>Success!</strong> Retrieved {count($users)} users from database.
                        <ul>
                            {foreach from=$users item=user}
                                <li>User ID: {$user.id|default:'N/A'} - Name: {$user.name|default:'N/A'}</li>
                            {/foreach}
                        </ul>
                    </div>
                {else}
                    <div class="debug-warning">
                        <strong>No users found</strong> or database connection failed.
                    </div>
                {/if}
                
                <button id="ajax-test" class="btn btn-primary">Test AJAX Request</button>
                <div id="ajax-result" style="margin-top: 10px;"></div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="debug-section">
                <h3>Configuration Test</h3>
                <p>Current application configuration:</p>
                
                <div class="debug-info">
                    <strong>App Name:</strong> {$config.name|default:'Not set'}<br>
                    <strong>Debug Mode:</strong> {if $config.debug}Enabled{else}Disabled{/if}<br>
                    <strong>Default Branch:</strong> {$config.default_branch|default:'Not set'}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="debug-section">
                <h3>System Information</h3>
                <div class="debug-info">
                    <strong>Timestamp:</strong> {$test_data.timestamp}<br>
                    <strong>Memory Usage:</strong> {($test_data.memory_usage/1024/1024)|string_format:"%.2f"} MB<br>
                    <strong>Peak Memory:</strong> {($test_data.peak_memory/1024/1024)|string_format:"%.2f"} MB<br>
                    <strong>PHP Version:</strong> {$PHP_VERSION}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="debug-section">
                <h3>Debug Bar Features</h3>
                <p>The debug bar at the bottom of this page should show:</p>
                <ul>
                    <li><strong>Messages:</strong> Custom log messages from this page</li>
                    <li><strong>Database:</strong> SQL queries executed during page load</li>
                    <li><strong>Timeline:</strong> Performance timing information</li>
                    <li><strong>Config:</strong> Application configuration data</li>
                    <li><strong>Request:</strong> HTTP request information</li>
                </ul>
                
                <div class="debug-info">
                    <strong>Tip:</strong> Click on the debug bar tabs to explore different types of debugging information.
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="footer"}
<script>
$(document).ready(function() {
    $('#ajax-test').click(function() {
        var button = $(this);
        var resultDiv = $('#ajax-result');
        
        button.prop('disabled', true).text('Loading...');
        resultDiv.html('<div class="alert alert-info">Making AJAX request...</div>');
        
        $.ajax({
            url: '/debug-test/ajax',
            method: 'GET',
            dataType: 'json',
            success: function(data) {
                if (data.success) {
                    resultDiv.html('<div class="alert alert-success"><strong>AJAX Success!</strong> User count: ' + data.user_count + ' (at ' + data.timestamp + ')</div>');
                } else {
                    resultDiv.html('<div class="alert alert-danger"><strong>AJAX Error:</strong> ' + data.error + '</div>');
                }
            },
            error: function(xhr, status, error) {
                resultDiv.html('<div class="alert alert-danger"><strong>AJAX Failed:</strong> ' + error + '</div>');
            },
            complete: function() {
                button.prop('disabled', false).text('Test AJAX Request');
            }
        });
    });
});
</script>
{/block}
