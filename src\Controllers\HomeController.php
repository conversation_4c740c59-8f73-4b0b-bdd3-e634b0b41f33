<?php

namespace App\Controllers;

use App\Controller\Controller;

/**
 * Home Controller
 *
 * Handles the main application pages including the landing page and
 * authenticated user dashboard. Includes branch-aware functionality.
 */
class HomeController extends Controller
{
    /**
     * Display the application landing page
     *
     * Currently redirects to the authenticated home page.
     * Contains commented database query examples for testing the query builder.
     *
     * @todo Implement proper landing page logic
     */
    public function index()
    {
        // Database query examples (commented for reference):
        // These examples demonstrate various query builder features:
        // - Table aliasing: DB()->table('users u')
        // - Column selection: select('id as i', 'user.email')
        // - WHERE conditions: where('username', '=', 'admin')
        // - Nested conditions: where(function ($q) { ... })
        // - OR conditions: orWhere('level', '>', 9999)
        // - Subqueries: where('id', '=', function ($q) { ... })
        // - Joins: leftJoin('vendors', 'vendors.user_id', '=', 'users.id')
        // - Aggregates: count(), exists()

        redirect(route('home'));
    }

    /**
     * Display the authenticated user dashboard
     *
     * Shows the home page for authenticated users with a list of all users.
     * Includes branch information and branch switching functionality.
     * Requires authentication middleware to access.
     */
    public function home()
    {
        $user = auth()->user();
        // logger($user);

        // abort("403", "aswd");
        // $count = DB()->table('users')->count('`count(*)`');

        // $user = currentUser();
        // logger("Username: " . $user['username']);

        // $userModel = $this->model('User');
        // $users = $userModel->getAllUsers();

        // // Get branch information
        // $currentBranch = $this->branchService->getCurrentBranch();
        // $availableBranches = $this->branchService->getAvailableBranches(
        //     isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null
        // );

        $this->view('home', [
            // 'users' => $users,
            // 'current_branch' => $currentBranch,
            // 'available_branches' => $availableBranches,
        ]);
    }
}
