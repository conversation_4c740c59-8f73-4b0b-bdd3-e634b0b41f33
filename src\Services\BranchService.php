<?php

namespace App\Services;

use App\Models\Branch;

/**
 * Branch Service
 * 
 * Handles branch-related business logic including branch switching,
 * configuration management, and session handling.
 */
class BranchService
{
    /** @var Branch */
    private $branchModel;

    /**
     * BranchService constructor
     * 
     * Initializes the branch service with required dependencies.
     */
    public function __construct()
    {
        $this->branchModel = new Branch();
    }

    /**
     * Get current user's branch from session
     * 
     * Retrieves the currently selected branch from the user session.
     * Falls back to user's default branch if no current branch is set.
     * 
     * @return array|null Current branch data or null if not set
     */
    public function getCurrentBranch()
    {
        // Check session first
        if (config('current_branch', null)) {
            return config('current_branch');
        }

        $branch = $this->branchModel->getBranchById($_SESSION['current_branch_id'] ?? config('app.default_branch', 1));
        if ($branch) {
            $this->setCurrentBranch($branch);
            return $branch;
        }

        return null;
    }

    /**
     * Set current branch in session
     *
     * Sets the current branch in the user session and updates
     * the user's current branch in the database. Ensures config
     * is properly stored for helper functions.
     *
     * @param array $branch Branch data
     */
    public function setCurrentBranch(array $branch)
    {
        config(['current_branch' => $branch]);

        $_SESSION['current_branch'] = $branch;
        $_SESSION['current_branch_id'] = $branch['id'];
        $_SESSION['current_branch_code'] = $branch['code'];
    }

    /**
     * Switch user's branch
     * 
     * Switches the user to a different branch after validating access permissions.
     * 
     * @param int $branchId The branch ID to switch to
     * @return bool True if switch successful, false otherwise
     * @throws \Exception If branch is invalid or user doesn't have access
     */
    public function switchBranch($branchId)
    {
        // Validate branch exists and is active
        $branch = $this->branchModel->getBranchById($branchId);
        if (!$branch) {
            throw new \Exception("Invalid branch selected");
        }

        // Check if user has access to this branch (if logged in)
        if (isset($_SESSION['user_id'])) {
            if (!$this->userHasAccessToBranch($_SESSION['user_id'], $branchId)) {
                throw new \Exception("Access denied to selected branch");
            }
        }

        // Set the new branch
        $this->setCurrentBranch($branch);

        return true;
    }

    /**
     * Check if user has access to branch
     *
     * Verifies if a user has permission to access a specific branch.
     *
     * @param int $userId The user ID
     * @param int $branchId The branch ID
     * @return bool True if user has access, false otherwise
     */
    public function userHasAccessToBranch($userId, $branchId)
    {
        // Get user's allowed branches using default connection to avoid circular dependency
        $user = DB()->table('user_branch')->where('user_id', $userId)->where('branch_id', $branchId)->first();

        if (!$user) {
            return false;
        }

        return true;
    }

    /**
     * Get available branches for user
     * 
     * Returns list of branches the user has access to.
     * 
     * @param int|null $userId The user ID (null for guest users)
     * @return array Array of available branches
     */
    public function getAvailableBranches($userId = null)
    {
        $allBranches = $this->branchModel->getAllActiveBranches();

        // If no user ID provided, return all active branches
        if (!$userId) {
            return $allBranches;
        }

        // // Filter branches based on user permissions using default connection
        $branches = DB()->table('branches')->select('branches.*')
            ->leftJoin('user_branch', 'branches.id', '=', 'user_branch.branch_id')
            ->where('user_id', $userId)->get();
        
        return $branches;        
    }

    /**
     * Clear branch session data
     * 
     * Removes all branch-related data from the session.
     */
    public function clearBranchSession()
    {
        unset($_SESSION['current_branch']);
        unset($_SESSION['current_branch_id']);
        unset($_SESSION['current_branch_code']);
    }
}
