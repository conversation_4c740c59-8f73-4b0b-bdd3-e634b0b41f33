<?php

namespace App\Models;

use App\Core\Model;

/**
 * UserBranch Model
 *
 * Handles user-branch relationships with composite primary key
 * This is a pivot table model with additional attributes
 */
class UserBranch extends Model
{
    /** @var string */
    protected static $table = 'user_branch';

    /** @var array - Composite primary key */
    protected static $primaryKey = ['user_id', 'branch_id'];

    /** @var array */
    protected static $fillable = [
        'user_id', 'branch_id', 'role', 'access_level', 'assigned_at'
    ];

    /** @var array */
    protected static $guarded = [];

    /** @var bool */
    protected static $timestamps = true;

    // ===== RELATIONSHIPS =====

    /**
     * Get the user that owns this relationship
     *
     * @return \App\Core\Relationship
     */
    public static function user()
    {
        return static::belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * Get the branch associated with this relationship
     *
     * @return \App\Core\Relationship
     */
    public static function branch()
    {
        return static::belongsTo(Branch::class, 'branch_id', 'id');
    }

    // ===== COMPOSITE KEY SPECIFIC METHODS =====

    /**
     * Find by user and branch IDs
     *
     * @param int $userId User ID
     * @param int $branchId Branch ID
     * @return \App\Core\ModelInstance|null
     */
    public static function findByUserAndBranch(int $userId, int $branchId): ?\App\Core\ModelInstance
    {
        return static::find([$userId, $branchId]);
    }

    /**
     * Get all relationships for a specific user
     *
     * @param int $userId User ID
     * @return array Array of UserBranch instances
     */
    public static function forUser(int $userId): array
    {
        $records = static::where('user_id', $userId)->get();
        return static::newCollection($records);
    }

    /**
     * Get all relationships for a specific branch
     *
     * @param int $branchId Branch ID
     * @return array Array of UserBranch instances
     */
    public static function forBranch(int $branchId): array
    {
        $records = static::where('branch_id', $branchId)->get();
        return static::newCollection($records);
    }

    /**
     * Create or update user-branch relationship
     *
     * @param int $userId User ID
     * @param int $branchId Branch ID
     * @param array $attributes Additional attributes
     * @return \App\Core\ModelInstance|bool
     */
    public static function createOrUpdate(int $userId, int $branchId, array $attributes = [])
    {
        $existing = static::findByUserAndBranch($userId, $branchId);
        
        if ($existing) {
            // Update existing
            $existing->update($attributes);
            return $existing;
        } else {
            // Create new
            $data = array_merge([
                'user_id' => $userId,
                'branch_id' => $branchId
            ], $attributes);
            
            return static::create($data);
        }
    }

    /**
     * Remove user-branch relationship
     *
     * @param int $userId User ID
     * @param int $branchId Branch ID
     * @return bool
     */
    public static function removeRelationship(int $userId, int $branchId): bool
    {
        return static::destroy([$userId, $branchId]);
    }
}
