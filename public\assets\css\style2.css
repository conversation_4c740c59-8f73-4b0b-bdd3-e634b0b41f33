.panel-login {
	max-width: 440px;
	border-color: #ccc;
	-webkit-box-shadow: 0px 2px 3px 0px rgba(0,0,0,0.2);
	-moz-box-shadow: 0px 2px 3px 0px rgba(0,0,0,0.2);
	box-shadow: 0px 2px 3px 0px rgba(0,0,0,0.2);
	margin-top: 20px;
}
.panel-login>.panel-heading {
	color: #00415d;
	background-color: #fff;
	border-color: #fff;
	text-align:center;
}
.panel-login>.panel-heading a{
	text-decoration: none;
	color: #666;
	font-weight: bold;
	font-size: 15px;
	-webkit-transition: all 0.1s linear;
	-moz-transition: all 0.1s linear;
	transition: all 0.1s linear;
}
.panel-login>.panel-heading a.active{
	color: #029f5b;
	font-size: 18px;
}
.panel-login>.panel-heading hr{
	margin-top: 10px;
	margin-bottom: 0px;
	clear: both;
	border: 0;
	height: 1px;
	background-image: -webkit-linear-gradient(left,rgba(0, 0, 0, 0),rgba(0, 0, 0, 0.15),rgba(0, 0, 0, 0));
	background-image: -moz-linear-gradient(left,rgba(0,0,0,0),rgba(0,0,0,0.15),rgba(0,0,0,0));
	background-image: -ms-linear-gradient(left,rgba(0,0,0,0),rgba(0,0,0,0.15),rgba(0,0,0,0));
	background-image: -o-linear-gradient(left,rgba(0,0,0,0),rgba(0,0,0,0.15),rgba(0,0,0,0));
}
.panel-login input[type="text"],.panel-login input[type="email"],.panel-login input[type="password"] {
	height: 45px;
	border: 1px solid #ddd;
	font-size: 16px;
	-webkit-transition: all 0.1s linear;
	-moz-transition: all 0.1s linear;
	transition: all 0.1s linear;
}
.panel-login input:hover,
.panel-login input:focus {
	outline:none;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
	border-color: #ccc;
}

.navbar-brand{
	padding: 5px 10px;
  background: #ffdf00;
}

.navbar .branch_code > a{
  vertical-align: middle;
	margin-left: 5px;
	font-size: 24px;
}

.navbar-default{
  color: #fff;
  background-color: #157347;
  border-color: #146c43;
}

.navbar-default .navbar-nav > li > a {
  color: #fff;
}

.navbar-default .navbar-nav > li > a:hover {
  color: red;
}

.input-group-addon > select {
	border:none;
	background-color: #eee;
}

.p0 {
	padding: 0 !important;
}

.pt0 {
	padding-top: 0 !important;
}

.pb0 {
	padding-bottom: 0 !important;
}

.m0 {
	margin: 0 !important;
}

.body-content{
	padding-bottom: 160px;
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 60px; /* Set the fixed height of the footer here */
	background-color: #f5f5f5;
	padding: 10px;
	border-top: #8f8e8e 1px solid;
}



div:where(.swal2-icon) {
  position:relative;
  box-sizing:content-box;
  justify-content:center;
  width:5em;
  height:5em;
  margin:2.5em auto .6em;
  zoom:var(--swal2-icon-zoom);
  border:.25em solid rgba(0,0,0,0);
  border-radius:50%;
  border-color:#000;
  font-family:inherit;
  line-height:5em;
  cursor:default;
  user-select:none;
}
div:where(.swal2-icon) .swal2-icon-content {
  display:flex;
  align-items:center;
  font-size:3.75em
}

div:where(.swal2-icon).swal2-success {
  border-color:#a5dc86;
  color:#a5dc86
}
div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line] {
  position:absolute;
  width:3.75em;
  height:7.5em;
  border-radius:50%
}
div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left] {
  top:-0.4375em;
  left:-2.0635em;
  transform:rotate(-45deg);
  transform-origin:3.75em 3.75em;
  border-radius:7.5em 0 0 7.5em
}
div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right] {
  top:-0.6875em;
  left:1.875em;
  transform:rotate(-45deg);
  transform-origin:0 3.75em;
  border-radius:0 7.5em 7.5em 0
}
div:where(.swal2-icon).swal2-success .swal2-success-ring {
  position:absolute;
  z-index:2;
  top:-0.25em;
  left:-0.25em;
  box-sizing:content-box;
  width:100%;
  height:100%;
  border:.25em solid rgba(165,220,134,.3);
  border-radius:50%
}
div:where(.swal2-icon).swal2-success .swal2-success-fix {
  position:absolute;
  z-index:1;
  top:.5em;
  left:1.625em;
  width:.4375em;
  height:5.625em;
  transform:rotate(-45deg)
}
div:where(.swal2-icon).swal2-success [class^=swal2-success-line] {
  display:block;
  position:absolute;
  z-index:2;
  height:.3125em;
  border-radius:.125em;
  background-color:#a5dc86
}
div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip] {
  top:2.875em;
  left:.8125em;
  width:1.5625em;
  transform:rotate(45deg)
}
div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long] {
  top:2.375em;
  right:.5em;
  width:2.9375em;
  transform:rotate(-45deg)
}


div:where(.swal2-icon).swal2-error {
  border-color:#f27474;
  color:#f27474
}
div:where(.swal2-icon).swal2-error .swal2-x-mark {
  position:relative;
  flex-grow:1
}
div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line] {
  display:block;
  position:absolute;
  top:2.3125em;
  width:2.9375em;
  height:.3125em;
  border-radius:.125em;
  background-color:#f27474
}
div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left] {
  left:1.0625em;
  transform:rotate(45deg)
}
div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right] {
  right:1em;
  transform:rotate(-45deg)
}

div:where(.swal2-icon).swal2-info {
  border-color:#3fc3ee;
  color:#3fc3ee
}

div:where(.swal2-icon).swal2-warning {
  border-color:#f8bb86;
  color:#f8bb86
}

div:where(.swal2-loading) {
  align-items:center;
  justify-content:center;
}

div:where(.swal2-loader) {
  align-items:center;
  justify-content:center;
  width:2.2em;
  height:2.2em;
  margin:0 1.875em;
  animation:swal2-rotate-loading 1.5s linear 0s infinite normal;
  border-width:.25em;
  border-style:solid;
  border-radius:100%;
  border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)
}

@keyframes swal2-rotate-loading {
  0% {
    transform:rotate(0deg)
  }
  100% {
    transform:rotate(360deg)
  }
}

.blockui {
  left: 50%;
  bottom: auto;
  right: auto;
  z-index: 1050;
  padding: 0;
  width: 500px;
    height: 60px;
  margin-left: -250px;
  background-color: transparent;
  border: none;
  border-radius: 6px;
  -webkit-box-shadow: none;
  box-shadow:none;
  background-clip: padding-box;
}

.pagination > li > a {
  color: #000;
}

.pagination > .active > a, .pagination > .active > a:focus, .pagination > .active > a:hover, .pagination > .active > span, .pagination > .active > span:focus, .pagination > .active > span:hover {
  z-index: 3;
  color: #adadad;
  cursor: default;
  background-color: #dbdbdb;
  border-color: #adadad;
}

.pagination > li > a:focus, .pagination > li > a:hover, .pagination > li > span:focus, .pagination > li > span:hover {
  z-index: 2;
  color: black;
  background-color: #eee;
  border-color: #ddd;
}