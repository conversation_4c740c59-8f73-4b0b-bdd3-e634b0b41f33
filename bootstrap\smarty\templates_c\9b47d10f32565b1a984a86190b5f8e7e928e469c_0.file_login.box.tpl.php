<?php
/* Smarty version 5.5.1, created on 2025-07-10 07:43:54
  from 'file:login.box.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.5.1',
  'unifunc' => 'content_686f531aa695f8_53658933',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '9b47d10f32565b1a984a86190b5f8e7e928e469c' => 
    array (
      0 => 'login.box.tpl',
      1 => 1752123665,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
))) {
function content_686f531aa695f8_53658933 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates';
?>
<div class="panel panel-login center-block">
	<div class="panel-heading">
		<?php if ($_smarty_tpl->getValue('error')) {?>
			<div class="text-danger text-left">
				<h4>
				Whoops! Something went wrong.
				</h4>
				<ul>
					<li><?php echo $_smarty_tpl->getValue('error');?>
</li>
				</ul>
			</div>
		<?php }?>
	</div>
	<div class="panel-body">
		<div class="row">
			<div class="col-lg-12">
				<form id="login-form" action="/login" method="post" role="form" style="display: block;">

										<div class="form-group">
						<label for="branch_id">Select Branch</label>
						<select class="form-control" name="branch_id" id="branch_id" required>
							<?php if ((true && ($_smarty_tpl->hasVariable('branches') && null !== ($_smarty_tpl->getValue('branches') ?? null)))) {?>
								<?php
$_from = $_smarty_tpl->getSmarty()->getRuntime('Foreach')->init($_smarty_tpl, $_smarty_tpl->getValue('branches'), 'branch');
$foreach0DoElse = true;
foreach ($_from ?? [] as $_smarty_tpl->getVariable('branch')->value) {
$foreach0DoElse = false;
?>
									<option value="<?php echo $_smarty_tpl->getValue('branch')['id'];?>
"
										<?php if ((true && ($_smarty_tpl->hasVariable('selected_branch') && null !== ($_smarty_tpl->getValue('selected_branch') ?? null))) && $_smarty_tpl->getValue('selected_branch') == $_smarty_tpl->getValue('branch')['id']) {?>selected<?php }?>>
											<?php echo $_smarty_tpl->getValue('branch')['code'];?>

									</option>
								<?php
}
$_smarty_tpl->getSmarty()->getRuntime('Foreach')->restore($_smarty_tpl, 1);?>
							<?php }?>
						</select>
					</div>

					<div class="form-group">
						<label for="user">Username</label>
						<input type="text" name="username" id="user" tabindex="2" class="form-control"
								value="<?php if ((true && (true && null !== ($_POST['username'] ?? null)))) {
echo $_POST['username'];
}?>" required>
					</div>
					<div class="form-group">
						<label for="pass">Password</label>
						<input type="password" name="password" id="pass" tabindex="3" class="form-control" required>
					</div>
					
					<div class="form-group">							
						<input type="submit" tabindex="4" class="form-control btn btn-success btn-block" value="Log In">
						
						<?php if (!$_smarty_tpl->getValue('hide_branch')) {?>
							<div class="text-center">
								OR
							</div>

							<input type="button" tabindex="4" class="form-control btn btn-warning btn-block" value="Go to Vendor Portal" 
								onclick="document.location.href='v/Dashboard'; return false;"
								/>	
						<?php }?>
					</div>
				</form>								
			</div>
		</div>
	</div>
</div><?php }
}
