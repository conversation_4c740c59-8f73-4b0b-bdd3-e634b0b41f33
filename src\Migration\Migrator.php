<?php

namespace App\Migration;

use App\Core\Database;

class Migrator
{
    private Database $db;
    private string $migrationPath;

    public function __construct(string $migrationPath = __DIR__ . '/../../migrations')
    {
        $this->db = DB();
        $this->migrationPath = $migrationPath;
        $this->createMigrationsTable();
    }

    private function createMigrationsTable(): void
    {
        $this->db->raw("
            CREATE TABLE IF NOT EXISTS migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
    }

    public function getAppliedMigrations(): array
    {
        return $this->db->table('migrations')->fetchColumn('migration');
    }

    public function saveMigration(string $migration): void
    {
        $this->db->table('migrations')->insert([
            'migration' => $migration,
        ]);        
    }

    public function migrate(): void
    {
        $applied = $this->getAppliedMigrations();
        $files = scandir($this->migrationPath);

        $toApply = array_filter($files, function ($file) use ($applied) {
            return pathinfo($file, PATHINFO_EXTENSION) === 'php' && !in_array($file, $applied);
        });

        foreach ($toApply as $file) {
            echo "Applying migration: $file\n";
            $migration = require $this->migrationPath . '/' . $file;

            if (method_exists($migration, 'up')) {
                $migration->up($this->db);
                $this->saveMigration($file);
                echo "Applied: $file\n";
            } else {
                echo "Migration $file is invalid (no 'up' method)\n";
            }
        }

        echo "All done.\n";
    }
}
