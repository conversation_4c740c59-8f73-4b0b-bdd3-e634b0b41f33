<?php

namespace App\Services;

use DebugBar\StandardDebugBar;
use DebugBar\DataCollector\PDO\PDOCollector;
use DebugBar\DataCollector\PDO\TraceablePDO;
use DebugBar\DataCollector\ConfigCollector;
use DebugBar\Storage\FileStorage;
use DebugBar\OpenHandler;

/**
 * Debug Bar Service
 *
 * Manages the PHP Debug Bar integration for development debugging.
 * Provides database query monitoring, configuration inspection, and custom logging.
 *
 * Production Usage:
 * - Set DEBUGBAR_FORCE_ENABLE=true to always enable (use with caution)
 * - Set DEBUGBAR_PRODUCTION_KEY=secret_key and access with ?debug_key=secret_key
 * - Or send debug_key in POST data or Cookie for secure production debugging
 */
class DebugBarService
{
    /** @var StandardDebugBar */
    private static $debugbar;
    
    /** @var bool */
    private static $enabled = false;
    
    /**
     * Initialize the debug bar
     *
     * @return StandardDebugBar|null
     */
    public static function init()
    {
        // Check if debug bar should be enabled
        if (!self::shouldEnable()) {
            return null;
        }
        
        if (self::$debugbar === null) {
            self::$debugbar = new StandardDebugBar();
            self::$enabled = true;

            // Configure storage for open handler
            self::configureStorage();

            // Add configuration collector
            self::addConfigCollector();

            // Add custom collectors
            self::addCustomCollectors();
        }
        
        return self::$debugbar;
    }
    
    /**
     * Get the debug bar instance
     * 
     * @return StandardDebugBar|null
     */
    public static function getDebugBar()
    {
        return self::$debugbar;
    }

    public static function getOpenHandler()
    {
        return new OpenHandler(self::$debugbar);
    }
    
    /**
     * Check if debug bar is enabled
     *
     * @return bool
     */
    public static function isEnabled()
    {
        return self::$enabled && self::$debugbar !== null;
    }

    /**
     * Determine if debug bar should be enabled
     *
     * Can be enabled in production with DEBUGBAR_FORCE_ENABLE=true
     * or DEBUGBAR_PRODUCTION_KEY with matching value
     *
     * @return bool
     */
    private static function shouldEnable()
    {
        // Force enable if explicitly set (useful for production debugging)
        if (config('app.env', 'development') != 'production') {
            return true;
        }

        if (isset($_SESSION['debugbar_enabled'])) {
            return true;
        }

        // Default: only enable in debug mode
        return filter_var(config('app.debug', false), FILTER_VALIDATE_BOOLEAN);
    }
    
    /**
     * Add a PDO collector for database query monitoring
     *
     * @param \PDO $pdo
     * @param string $name
     * @return TraceablePDO|null
     */
    public static function addPDOCollector(\PDO $pdo, $name = 'default')
    {
        if (!self::isEnabled()) {
            return null;
        }

        // Create a traceable PDO wrapper
        $traceablePdo = new TraceablePDO($pdo);

        // Check if we already have a PDO collector
        if (!self::$debugbar->hasCollector('pdo')) {
            $pdoCollector = new PDOCollector();
            self::$debugbar->addCollector($pdoCollector);
        }

        // Add the connection to the existing collector
        self::$debugbar['pdo']->addConnection($traceablePdo, $name);

        return $traceablePdo;
    }
    
    /**
     * Configure storage for debug bar data persistence
     */
    private static function configureStorage()
    {
        if (!self::isEnabled()) {
            return;
        }

        // Create storage directory if it doesn't exist
        $storageDir = __DIR__ . '/../../logs/debugbar';
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0755, true);
        }

        // Set up file storage
        $storage = new FileStorage($storageDir);
        self::$debugbar->setStorage($storage);
    }

    /**
     * Add configuration collector
     */
    private static function addConfigCollector()
    {
        if (!self::isEnabled()) {
            return;
        }
        
        $configData = [
            'cached' => config('cached', false),
            'app' => config('app', []),
            'database' => config('databases.default', []),
            'session' => [
                'user_id' => $_SESSION['user_id'] ?? null,
                'current_branch' => $_SESSION['current_branch'] ?? null,
                'login' => $_SESSION['login'] ?? false,
            ],
            'environment' => [
                'PHP_VERSION' => PHP_VERSION,
                'SERVER_SOFTWARE' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
                'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
            ]
        ];
        
        $configCollector = new ConfigCollector($configData, 'Config');
        self::$debugbar->addCollector($configCollector);
    }
    
    /**
     * Add custom collectors for application-specific data
     */
    private static function addCustomCollectors()
    {
        if (!self::isEnabled()) {
            return;
        }
        
        // Add route information
        self::$debugbar['messages']->addMessage('Route dispatched', 'info');
        
        // Add current user info if logged in
        if (isset($_SESSION['user_id'])) {
            self::$debugbar['messages']->addMessage('User ID: ' . $_SESSION['user_id'], 'info');
        }
        
        // Add current branch info
        if (isset($_SESSION['current_branch'])) {
            $branch = $_SESSION['current_branch'];
            self::$debugbar['messages']->addMessage('Current Branch: ' . ($branch['code'] ?? 'Unknown'), 'info');
        }
    }
    
    /**
     * Log a message to the debug bar
     * 
     * @param string $message
     * @param string $level (debug, info, notice, warning, error, critical, alert, emergency)
     */
    public static function log($message, $level = 'info')
    {
        if (!self::isEnabled()) {
            return;
        }
        
        self::$debugbar['messages']->addMessage($message, $level);
    }
    
    /**
     * Add timing information
     * 
     * @param string $name
     * @param callable $callback
     * @return mixed
     */
    public static function measure($name, callable $callback)
    {
        if (!self::isEnabled()) {
            return $callback();
        }
        
        self::$debugbar['time']->startMeasure($name);
        $result = $callback();
        self::$debugbar['time']->stopMeasure($name);
        
        return $result;
    }
    
    /**
     * Get the debug bar renderer
     * 
     * @return \DebugBar\JavascriptRenderer|null
     */
    public static function getRenderer()
    {
        if (!self::isEnabled()) {
            return null;
        }
        
        $renderer = self::$debugbar->getJavascriptRenderer();

        // Set the base URL for debug bar assets
        $renderer->setBaseUrl('/vendor/php-debugbar/php-debugbar/src/DebugBar/Resources');

        // Set the open handler URL for AJAX requests
        $renderer->setOpenHandlerUrl('/debugbar/openhandler');

        return $renderer;
    }
    
    /**
     * Render debug bar head content (CSS)
     * 
     * @return string
     */
    public static function renderHead()
    {
        $renderer = self::getRenderer();
        if (!$renderer) {
            return '';
        }
        
        return $renderer->renderHead();
    }
    
    /**
     * Render debug bar body content (JavaScript and HTML)
     * 
     * @return string
     */
    public static function render()
    {
        $renderer = self::getRenderer();
        if (!$renderer) {
            return '';
        }
        
        return $renderer->render();
    }
    
    /**
     * Add exception information to debug bar
     * 
     * @param \Throwable $exception
     */
    public static function addException(\Throwable $exception)
    {
        if (!self::isEnabled()) {
            return;
        }
        
        self::$debugbar['exceptions']->addThrowable($exception);
    }

    /**
     * Set debug key cookie for production debugging
     *
     * @param string $key The debug key
     * @param int $expiry Cookie expiry time (default: 1 hour)
     */
    public static function setDebugCookie($key, $expiry = 3600)
    {
        setcookie('debug_key', $key, time() + $expiry, '/', '', false, true);
    }

    /**
     * Clear debug key cookie
     */
    public static function clearDebugCookie()
    {
        setcookie('debug_key', '', time() - 3600, '/', '', false, true);
    }
}
