<?php

namespace App\Core\Extension;

use Smarty\Extension\Base;

class MyExtension extends Base {

    // public function getModifierCompiler(string $modifier): ?\Smarty\Compile\Modifier\ModifierCompilerInterface {

    //     // switch ($modifier) {
    //     //     case 'array_escape': return new MyArrayEscapeModifierCompiler();
    //     //     case 'array_unescape': return new MyArrayUnescapeModifierCompiler();
    //     // }

    //     // return null;
    // }
}