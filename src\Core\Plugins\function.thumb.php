<?php

function smarty_function_thumb(array $params, Smarty_Internal_Template $template){

	$sku_id=intval($params['sku_id']);
	$img=$params['img'];
	$newwidth=intval($params['w']);
	$newheight=intval($params['h']);
	if($params['q']) $quality=intval($params['q']);
	else $quality=90;
	$clear_cache=intval($params['cc']);
	if($params['folder']) $folder=$params['folder'];
	else $folder="sku/";

	$imageinfo = pathinfo($img);

	$img_name=$imageinfo['filename'];
	$img_ext=$imageinfo['extension'];

	$fromFile=$img;
	$toFile=\Util::get_sku_img_thumb_dir($sku_id, $folder).$img_name."-".$newwidth."x".$newheight.".".$img_ext;

	if($clear_cache && file_exists($imgthumb)) unlink($imgthumb);
	if(file_exists($toFile) && filemtime($fromFile) > filemtime($toFile)) unlink($toFile);

	if(!file_exists($toFile)){
		$opt=array();
		$opt['cache']=1;
		$opt['width']=$newwidth;
		$opt['height']=$newheight;
		$opt['quality']=$quality;

		$ret=\Util::image_thumb($fromFile, $toFile, $opt);
		echo $ret['img'];
	}
	else{
		echo $toFile;
	}
}

?>
