<?php

namespace App\Core;

use App\Services\DebugBarService;
use Smarty\Smarty;
use Smarty\Template as SmartyTemplate;

class Template
{
    private static $tpl;
    
    /** @var \Smarty Smarty template engine instance */
    protected $smarty;

    private final function  __construct() 
    {
        // Log template rendering in debug bar
        logger("Template: " . config('tpl'), 'info');
        
        $paths = config('tpl_path')[config('tpl')];

        $this->smarty = new Smarty();
        $this->smarty->setTemplateDir($paths['template_dir']);
        $this->smarty->setCompileDir($paths['compile_dir']);
        $this->smarty->setCacheDir($paths['cache_dir']);
        $this->smarty->setConfigDir($paths['config_dir']);

        // Assign common variables
        $this->smarty->assign('PHP_VERSION', PHP_VERSION);

        // Register helper functions for use in templates
        $this->registerHelperFunctions();

        // Add debug bar renderer to all templates
        $this->setupDebugBar();

        
    }

    public static function getTpl() 
    {
        if(!isset(self::$tpl)) {
            self::$tpl = new Template;
        }
        return self::$tpl;
    }    

    /**
     * Setup debug bar for templates
     *
     * Assigns the debug bar renderer to Smarty templates if debug mode is enabled.
     */
    private function setupDebugBar()
    {
        if (DebugBarService::isEnabled()) {
            $renderer = DebugBarService::getRenderer();
            $this->smarty->assign('debugbarRenderer', $renderer);
        }
    }

    protected function addExtensions(): void
    {
        // Add custom extensions here
        // $this->smarty->setExtensions([
        //     new Smarty\Extension\CoreExtension(),
        //     new MyCustomExtension(),
        //     new Smarty\Extension\DefaultExtension(),
        // ]);
    }

    /**
     * Register helper functions for use in Smarty templates
     * In Smarty 5.5, all PHP functions must be registered before use
     */
    protected function registerHelperFunctions(): void
    {
        // Register the config() helper function
        $this->smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'config', function($params) {
            $key = $params['key'] ?? '';
            $default = $params['default'] ?? null;

            if (empty($key)) {
                return $default;
            }

            return config($key, $default);
        });

        // Register route() helper function
        $this->smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'route', function($params) {
            $name = $params['name'] ?? '';
            if (empty($name)) {
                return '';
            }
            return route($name);
        });

        // Register currentUser() helper function
        $this->smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'can', function($params) {
            return auth()->can($params['key'], $params['branch_id'] ?? 0);
        });

        $this->smarty->registerPlugin(Smarty::PLUGIN_BLOCK, 'access', [$this, 'smarty_block_access']);

        $this->smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'ifzero', [$this, 'smarty_modifier_ifzero']);
        // $this->smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'isApiRequest', 'isApiRequest');        

        // // Register common PHP functions as modifiers
        $this->smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'date_format', 'date');
        $this->smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'number_format', 'number_format');
        // $this->smarty->registerPlugin('modifier', 'ucfirst', 'ucfirst');
        // $this->smarty->registerPlugin('modifier', 'strtolower', 'strtolower');
        // $this->smarty->registerPlugin('modifier', 'strtoupper', 'strtoupper');
        // $this->smarty->registerPlugin('modifier', 'trim', 'trim');
        // $this->smarty->registerPlugin('modifier', 'substr', 'substr');
        // $this->smarty->registerPlugin('modifier', 'strlen', 'strlen');
        // $this->smarty->registerPlugin('modifier', 'htmlspecialchars', 'htmlspecialchars');
        // $this->smarty->registerPlugin('modifier', 'nl2br', 'nl2br');
        // $this->smarty->registerPlugin('modifier', 'strip_tags', 'strip_tags');
        // $this->smarty->registerPlugin('modifier', 'urlencode', 'urlencode');
        // $this->smarty->registerPlugin('modifier', 'urldecode', 'urldecode');
        // $this->smarty->registerPlugin('modifier', 'json_encode', 'json_encode');
        $this->smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'print_r', 'print_r');

        // // Register array functions
        $this->smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'count', 'count');
        $this->smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'implode', 'implode');
        // $this->smarty->registerPlugin('modifier', 'array_keys', 'array_keys');
        // $this->smarty->registerPlugin('modifier', 'array_values', 'array_values');        
    }

    /**
     * Render a view template
     *
     * Assigns data variables to the template and renders the specified template file.
     * Template files should have .tpl extension and be located in the configured template directory.
     *
     * @param string $template Template name (without .tpl extension)
     * @param array $data Associative array of data to pass to template
     */
    public function view($template, array $data = [])
    {
        foreach ($data as $key => $value) {
            $this->smarty->assign($key, $value);
        }

        // Log template rendering in debug bar
        logger("Rendering template: {$template}.tpl", 'info');

        $this->smarty->display($template . '.tpl');
    }

    public function assign($key, $value)
    {
        $this->smarty->assign($key, $value);
    }

    function smarty_modifier_ifzero($string, $default='')
    {
        $str=floatval($string);

        if($str==0 || $string=="0000-00-00 00:00:00" || $string=="0000-00-00") return $default;

        return $string;
    }

    function smarty_block_access($params, $content, SmartyTemplate $template, &$repeat)
    {
        if (!$repeat){
            if(auth()->can($params['key'], $params['branch_id'] ?? 0)) {
                return $content;
            } else {
                return null;
            }
        }
    }
}
