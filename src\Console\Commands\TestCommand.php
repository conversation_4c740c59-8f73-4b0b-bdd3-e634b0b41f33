<?php

namespace App\Console\Commands;

class TestCommand
{
    public function handle(array $args): void
    {
        // print_r($args);
        // print_r(config('app'));

        // $db = DB();
        // $db2 = DB()->connection('default2');
        // $db2 = DB('default3');
        
        // $users = $db2->table('users')->get();

        // print_r($users);

        // $db = DB();

        // $stmt = $db->table('users')->getStatement();

        // while ($row = $stmt->fetch()) {
        //     // Process each row
        //     print_r($row);
        // }

        // $users = $db->table('users')->get();
        // print_r($users);

        // $user = $db->table('users')->first();
        // print_r($user);

        $db = DB('default3');
        
        $stmt = $db->table('user')->where('active', 1)->limit(5)->getStatement();

        while ($row = $stmt->fetch()) {

            $user_branch = DB('default3')->table('user_privilege')->where('user_id', $row['id'])->first();

            // Process each row
            print_r($row);
            print_r($user_branch);
            print_r("===========================");
        }
    }
}
