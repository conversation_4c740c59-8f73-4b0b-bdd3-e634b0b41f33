<?php

namespace App\Controllers\API;

use App\Controller\Controller;
use App\Services\BranchService;

/**
 * Home Controller
 *
 * Handles the main application pages including the landing page and
 * authenticated user dashboard. Includes branch-aware functionality.
 */
class TestController extends Controller
{
    /** @var BranchService */
    private $branchService;

    /**
     * HomeController constructor
     *
     * Initializes the home controller with branch service.
     */
    public function __construct()
    {
        parent::__construct();
        $this->branchService = new BranchService();
    }

    /**
     * Display the application landing page
     *
     * Currently redirects to the authenticated home page.
     * Contains commented database query examples for testing the query builder.
     *
     * @todo Implement proper landing page logic
     */
    public function index()
    {
        $this->jsonSuccess([
            'message' => 'API test route', 
            'branch' => $this->branchService->getCurrentBranch()
        ]);
    }
}
