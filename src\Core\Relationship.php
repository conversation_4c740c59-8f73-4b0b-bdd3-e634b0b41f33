<?php

namespace App\Core;

/**
 * Relationship Class
 *
 * Handles model relationships like hasOne, hasMany, belongsTo, and belongsToMany.
 * Provides lazy loading and eager loading capabilities.
 */
class Relationship
{
    /** @var string */
    protected $type;

    /** @var string */
    protected $parent;

    /** @var string */
    protected $related;

    /** @var string */
    protected $foreignKey;

    /** @var string */
    protected $localKey;

    /** @var string|null */
    protected $pivotTable;

    /** @var ModelInstance|null */
    protected $parentInstance;

    /**
     * Relationship constructor
     *
     * @param string $type Relationship type (hasOne, hasMany, belongsTo, belongsToMany)
     * @param string $parent Parent model class
     * @param string $related Related model class
     * @param string $foreignKey Foreign key
     * @param string $localKey Local key
     * @param string|null $pivotTable Pivot table for many-to-many relationships
     */
    public function __construct(string $type, string $parent, string $related, string $foreignKey, string $localKey, ?string $pivotTable = null)
    {
        $this->type = $type;
        $this->parent = $parent;
        $this->related = $related;
        $this->foreignKey = $foreignKey;
        $this->localKey = $localKey;
        $this->pivotTable = $pivotTable;
    }

    /**
     * Set the parent instance for this relationship
     *
     * @param ModelInstance $instance Parent model instance
     * @return self
     */
    public function setParent(ModelInstance $instance): self
    {
        $this->parentInstance = $instance;
        return $this;
    }

    /**
     * Get the related model(s)
     *
     * @return ModelInstance|array|null
     * @throws \Exception If relationship type is invalid or database operation fails
     */
    public function get()
    {
        if (!$this->parentInstance) {
            throw new \Exception("Parent instance not set for relationship");
        }

        $parentKey = $this->parentInstance->getAttribute($this->localKey);
        if (!$parentKey) {
            return $this->type === 'hasMany' || $this->type === 'belongsToMany' ? [] : null;
        }

        switch ($this->type) {
            case 'hasOne':
                return $this->getHasOne($parentKey);
            case 'hasMany':
                return $this->getHasMany($parentKey);
            case 'belongsTo':
                return $this->getBelongsTo($parentKey);
            case 'belongsToMany':
                return $this->getBelongsToMany($parentKey);
            default:
                throw new \Exception("Invalid relationship type: {$this->type}");
        }
    }

    /**
     * Get hasOne relationship result
     *
     * @param mixed $parentKey Parent key value
     * @return ModelInstance|null
     */
    protected function getHasOne($parentKey): ?ModelInstance
    {
        $relatedModel = $this->related;
        return $relatedModel::whereFirst($this->foreignKey, $parentKey);
    }

    /**
     * Get hasMany relationship result
     *
     * @param mixed $parentKey Parent key value
     * @return array
     */
    protected function getHasMany($parentKey): array
    {
        $relatedModel = $this->related;
        $records = $relatedModel::where($this->foreignKey, $parentKey)->get();
        return $relatedModel::newCollection($records);
    }

    /**
     * Get belongsTo relationship result
     *
     * @param mixed $foreignKeyValue Foreign key value
     * @return ModelInstance|null
     */
    protected function getBelongsTo($foreignKeyValue): ?ModelInstance
    {
        $relatedModel = $this->related;dd($relatedModel);
        return $relatedModel::find($foreignKeyValue);
    }

    /**
     * Get belongsToMany relationship result
     *
     * @param mixed $parentKey Parent key value
     * @return array
     */
    protected function getBelongsToMany($parentKey): array
    {
        $relatedModel = $this->related;
        $relatedTable = $relatedModel::getTable();
        $relatedPrimaryKey = $relatedModel::$primaryKey;

        // Query the pivot table to get related IDs
        $pivotRecords = DB()->table($this->pivotTable)
            ->where($this->foreignKey, $parentKey)
            ->get();

        if (empty($pivotRecords)) {
            return [];
        }

        // Extract related IDs
        $relatedIds = array_column($pivotRecords, $this->localKey);

        // Get related models
        $records = $relatedModel::findMany($relatedIds);
        
        // Add pivot data to each record
        foreach ($records as $record) {
            $pivotData = null;
            foreach ($pivotRecords as $pivotRecord) {
                if ($pivotRecord[$this->localKey] == $record->getAttribute($relatedPrimaryKey)) {
                    $pivotData = $pivotRecord;
                    break;
                }
            }
            $record->setAttribute('pivot', $pivotData);
        }

        return $records;
    }

    /**
     * Add a where clause to the relationship query
     *
     * @param string $column Column name
     * @param mixed $operator Operator or value
     * @param mixed $value Value (optional)
     * @return self
     */
    public function where(string $column, $operator = null, $value = null): self
    {
        // This would be implemented for more advanced relationship querying
        // For now, we'll keep it simple
        return $this;
    }

    /**
     * Get the first result from the relationship
     *
     * @return ModelInstance|null
     */
    public function first(): ?ModelInstance
    {
        $result = $this->get();
        
        if (is_array($result)) {
            return $result[0] ?? null;
        }
        
        return $result;
    }

    /**
     * Count the related models
     *
     * @return int
     */
    public function count(): int
    {
        if (!$this->parentInstance) {
            return 0;
        }

        $parentKey = $this->parentInstance->getAttribute($this->localKey);
        if (!$parentKey) {
            return 0;
        }

        switch ($this->type) {
            case 'hasMany':
                $relatedModel = $this->related;
                return $relatedModel::where($this->foreignKey, $parentKey)->count();
            case 'belongsToMany':
                return DB()->table($this->pivotTable)
                    ->where($this->foreignKey, $parentKey)
                    ->count();
            default:
                $result = $this->get();
                return $result ? 1 : 0;
        }
    }

    /**
     * Create a new related model
     *
     * @param array $attributes Model attributes
     * @return ModelInstance|bool
     */
    public function create(array $attributes = [])
    {
        if (!$this->parentInstance) {
            throw new \Exception("Parent instance not set for relationship");
        }

        $parentKey = $this->parentInstance->getAttribute($this->localKey);
        
        switch ($this->type) {
            case 'hasOne':
            case 'hasMany':
                $attributes[$this->foreignKey] = $parentKey;
                $relatedModel = $this->related;
                return $relatedModel::create($attributes);
            default:
                throw new \Exception("Create not supported for {$this->type} relationships");
        }
    }

    /**
     * Associate a model with this relationship (for belongsTo)
     *
     * @param ModelInstance $model Model to associate
     * @return bool
     */
    public function associate(ModelInstance $model): bool
    {
        if ($this->type !== 'belongsTo') {
            throw new \Exception("Associate only supported for belongsTo relationships");
        }

        if (!$this->parentInstance) {
            throw new \Exception("Parent instance not set for relationship");
        }

        $relatedKey = $model->getAttribute($this->localKey);
        $this->parentInstance->setAttribute($this->foreignKey, $relatedKey);
        
        return $this->parentInstance->save();
    }

    /**
     * Attach a model to a many-to-many relationship
     *
     * @param mixed $id Model ID or ModelInstance
     * @param array $pivotData Additional pivot data
     * @return bool
     */
    public function attach($id, array $pivotData = []): bool
    {
        if ($this->type !== 'belongsToMany') {
            throw new \Exception("Attach only supported for belongsToMany relationships");
        }

        if (!$this->parentInstance) {
            throw new \Exception("Parent instance not set for relationship");
        }

        $parentKey = $this->parentInstance->getAttribute($this->localKey);
        $relatedKey = $id instanceof ModelInstance ? $id->getKey() : $id;

        $data = array_merge([
            $this->foreignKey => $parentKey,
            $this->localKey => $relatedKey
        ], $pivotData);

        return DB()->table($this->pivotTable)->insert($data);
    }

    /**
     * Detach a model from a many-to-many relationship
     *
     * @param mixed $id Model ID or ModelInstance (optional, detaches all if not provided)
     * @return bool
     */
    public function detach($id = null): bool
    {
        if ($this->type !== 'belongsToMany') {
            throw new \Exception("Detach only supported for belongsToMany relationships");
        }

        if (!$this->parentInstance) {
            throw new \Exception("Parent instance not set for relationship");
        }

        $parentKey = $this->parentInstance->getAttribute($this->localKey);
        $query = DB()->table($this->pivotTable)->where($this->foreignKey, $parentKey);

        if ($id !== null) {
            $relatedKey = $id instanceof ModelInstance ? $id->getKey() : $id;
            $query->where($this->localKey, $relatedKey);
        }

        return $query->delete();
    }
}
