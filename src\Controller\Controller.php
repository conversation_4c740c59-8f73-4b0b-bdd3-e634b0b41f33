<?php

namespace App\Controller;

use App\Core\Template;
use App\Exceptions\HttpException;
use App\Exceptions\NotFoundException;
use App\Exceptions\ForbiddenException;
use App\Exceptions\UnauthorizedException;
use App\Exceptions\ValidationException;
use App\Services\BranchService;

/**
 * Base Controller Class
 *
 * Provides common functionality for all application controllers including
 * Template engine integration and model instantiation.
 */
class Controller
{
    /** @var BranchService */
    protected $branchService;    

    /** @var  Template engine instance */
    protected $tpl;

    /**
     * Controller constructor
     *
     * Initializes the Template engine with configured paths.
     * Sets up template, compile, cache, and config directories.
     *
     * @throws \Exception If Template configuration is invalid
     */
    public function __construct()
    {
        $this->tpl = Template::getTpl();

        $this->branchService = new BranchService();        

        $this->assign('currentBranch', $this->branchService->getCurrentBranch());

        // Assign logged-in user data
        $this->assignUserData();
    }

    /**
     * Assign logged-in user data to templates
     *
     * Makes current user information available in all Smarty templates.
     */
    private function assignUserData()
    {
        $currentUser = auth()->user();

        $this->assign('currentUser', $currentUser);
        $this->assign('isLoggedIn', $currentUser ? true : false);
    }

    public function assign($key, $value)
    {
        $this->tpl->assign($key, $value);
    }

    /**
     * Render a view template
     *
     * Assigns data variables to the template and renders the specified template file.
     * Template files should have .tpl extension and be located in the configured template directory.
     *
     * @param string $template Template name (without .tpl extension)
     * @param array $data Associative array of data to pass to template
     */
    public function view($template, array $data = [])
    {
        $this->tpl->view($template, $data);
    }

    /**
     * Instantiate a model class
     *
     * Creates and returns an instance of the specified model class.
     * Model classes should be located in the App\Models namespace.
     *
     * @param string $model Model class name (without namespace)
     * @return object Instance of the requested model
     * @throws \Exception If model class does not exist
     */
    public function model($model)
    {
        $modelClass = "App\\Models\\{$model}";
        return new $modelClass();
    }

    // ===== ERROR RESPONSE METHODS =====

    /**
     * Return a JSON error response
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code
     * @param array $data Additional error data
     * @param array $headers Additional headers
     */
    protected function jsonError(string $message, int $statusCode = 500, array $data = [], array $headers = []): void
    {
        jsonError($message, $statusCode, $data, $headers);
    }

    /**
     * Return a JSON success response
     *
     * @param mixed $data Response data
     * @param string $message Success message
     * @param int $statusCode HTTP status code
     */
    protected function jsonSuccess($data = null, string $message = 'Success', int $statusCode = 200): void
    {
        jsonSuccess($data, $message, $statusCode);
    }

    /**
     * Abort with 404 Not Found
     *
     * @param string $message Custom error message
     */
    protected function abort404(string $message = 'The requested resource could not be found.'): void
    {
        throw new NotFoundException($message);
    }

    /**
     * Abort with 403 Forbidden
     *
     * @param string $message Custom error message
     */
    protected function abort403(string $message = 'You do not have permission to access this resource.'): void
    {
        throw new ForbiddenException($message);
    }

    /**
     * Abort with 401 Unauthorized
     *
     * @param string $message Custom error message
     */
    protected function abort401(string $message = 'Authentication is required to access this resource.'): void
    {
        throw new UnauthorizedException($message);
    }

    /**
     * Abort with validation error
     *
     * @param array $validationErrors Validation error messages
     * @param string $message Custom error message
     */
    protected function abortValidation(array $validationErrors, string $message = 'The request data could not be processed.'): void
    {
        throw new ValidationException($validationErrors, $message);
    }

    /**
     * Abort with custom HTTP status code
     *
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    protected function abort(int $statusCode, string $message = '', array $data = []): void
    {
        throw new HttpException($statusCode, $message, $data);
    }

    /**
     * Abort if condition is true
     *
     * @param bool $condition Condition to check
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    protected function abortIf(bool $condition, int $statusCode, string $message = '', array $data = []): void
    {
        if ($condition) {
            $this->abort($statusCode, $message, $data);
        }
    }

    /**
     * Abort unless condition is true
     *
     * @param bool $condition Condition to check
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    protected function abortUnless(bool $condition, int $statusCode, string $message = '', array $data = []): void
    {
        if (!$condition) {
            $this->abort($statusCode, $message, $data);
        }
    } 
}
