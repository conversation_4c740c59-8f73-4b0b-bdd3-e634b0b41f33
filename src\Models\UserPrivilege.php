<?php

namespace App\Models;

use App\Core\Model;

/**
 * UserPrivilege Model
 *
 * Handles user privilege relationships and permissions
 */
class UserPrivilege extends Model
{
    /** @var string */
    protected static $table = 'user_privilege';

    /** @var array */
    protected static $fillable = [
        'user_id', 'privilege_id', 'branch_id'
    ];

    // ===== RELATIONSHIPS =====

    /**
     * Get the user that owns this privilege
     *
     * @return \App\Core\Relationship
     */
    public static function user()
    {
        return static::belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * Get the branch associated with this privilege
     *
     * @return \App\Core\Relationship
     */
    public static function branch()
    {
        return static::belongsTo(Branch::class, 'branch_id', 'id');
    }
}
