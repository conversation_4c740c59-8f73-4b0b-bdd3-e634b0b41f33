<?php

namespace App\Exceptions;

/**
 * 401 Unauthorized Exception
 * 
 * Thrown when authentication is required but not provided or invalid.
 */
class UnauthorizedException extends HttpException
{
    public function __construct(
        string $message = 'Authentication is required to access this resource.',
        array $errorData = [],
        array $headers = []
    ) {
        parent::__construct(401, $message, $errorData, $headers);
    }
}
