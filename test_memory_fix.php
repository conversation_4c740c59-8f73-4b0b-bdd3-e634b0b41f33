<?php

/**
 * Test script to verify the memory issue fix
 * 
 * This script tests that the circular dependency has been resolved
 * and the helper functions work correctly.
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "Testing memory fix for circular dependency...\n\n";

// Test 1: Basic DB() function call
echo "Test 1: Basic DB() function call\n";
try {
    $db = DB();
    echo "✓ DB() function works without memory issues\n";
} catch (Exception $e) {
    echo "✗ DB() function failed: " . $e->getMessage() . "\n";
}

// Test 2: DB() with explicit connection
echo "\nTest 2: DB() with explicit connection\n";
try {
    $db = DB();
    echo "✓ DB() function works\n";
} catch (Exception $e) {
    echo "✗ DB() function failed: " . $e->getMessage() . "\n";
}

// Test 3: currentBranch() helper function
echo "\nTest 3: currentBranch() helper function\n";
try {
    $branch = currentBranch();
    if ($branch === null) {
        echo "✓ currentBranch() returns null (no branch set)\n";
    } else {
        echo "✓ currentBranch() returns: " . json_encode($branch) . "\n";
    }
} catch (Exception $e) {
    echo "✗ currentBranch() function failed: " . $e->getMessage() . "\n";
}

// Test 4: Simulate branch in session
echo "\nTest 4: Simulate branch in session\n";
$_SESSION['current_branch'] = [
    'id' => 1,
    'code' => 'TEST',
    'description' => 'Test Branch',
];

try {
    $branch = currentBranch();
    echo "✓ currentBranch() with session: " . $branch['code'] . "\n";
    
    $db = DB(); // Should use 'default' connection from branch
    echo "✓ DB() with branch in session works\n";
    
} catch (Exception $e) {
    echo "✗ Session-based branch test failed: " . $e->getMessage() . "\n";
}

// Test 5: Memory usage check
echo "\nTest 6: Memory usage check\n";
$memoryBefore = memory_get_usage();
for ($i = 0; $i < 100; $i++) {
    $db = DB();
    $branch = currentBranch();
}
$memoryAfter = memory_get_usage();
$memoryDiff = $memoryAfter - $memoryBefore;

echo "Memory usage difference: " . number_format($memoryDiff) . " bytes\n";
if ($memoryDiff < 1000000) { // Less than 1MB increase
    echo "✓ Memory usage is stable (no memory leak)\n";
} else {
    echo "⚠ Memory usage increased significantly\n";
}

echo "\nAll tests completed!\n";
echo "Peak memory usage: " . number_format(memory_get_peak_usage()) . " bytes\n";

// Clean up
unset($_SESSION['current_branch']);
