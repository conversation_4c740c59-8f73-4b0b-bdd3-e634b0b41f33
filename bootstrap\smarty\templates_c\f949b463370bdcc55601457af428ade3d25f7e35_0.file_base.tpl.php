<?php
/* Smarty version 5.5.1, created on 2025-07-10 07:21:48
  from 'file:base.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.5.1',
  'unifunc' => 'content_686f4dece334c9_56159570',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'f949b463370bdcc55601457af428ade3d25f7e35' => 
    array (
      0 => 'base.tpl',
      1 => 1751789985,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
))) {
function content_686f4dece334c9_56159570 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates';
$_smarty_tpl->getInheritance()->init($_smarty_tpl, false);
?>
<!doctype html>
<html>
    <head>
        <?php $_smarty_tpl->assign('script_rev', "?v=202506201409", false, NULL);?>
        <base href="/" />
        <meta name="robots" content="noindex,nofollow">
        <meta name="googlebot" content="noindex,nofollow">
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title><?php echo $_smarty_tpl->getSmarty()->getFunctionHandler('config')->handle(array('key'=>'app.name','default'=>'APP'), $_smarty_tpl);?>
</title>
        <link href="assets/bootstrap-3.4.1/css/bootstrap.min.css<?php echo $_smarty_tpl->getValue('script_rev');?>
" rel="stylesheet">
        <link href="assets/bootstrap-submenu/css/bootstrap-submenu.min.css<?php echo $_smarty_tpl->getValue('script_rev');?>
" rel="stylesheet">
        <link href="assets/bootstrap-modal/css/bootstrap-modal.css<?php echo $_smarty_tpl->getValue('script_rev');?>
" rel="stylesheet">
        <link href="assets/bootstrap-modal/css/bootstrap-modal-bs3patch.css<?php echo $_smarty_tpl->getValue('script_rev');?>
" rel="stylesheet">
        <link href="assets/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css<?php echo $_smarty_tpl->getValue('script_rev');?>
" rel="stylesheet">
        <link href="assets/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet">
        <link href="assets/css/style.css<?php echo $_smarty_tpl->getValue('script_rev');?>
" rel="stylesheet">
        <link href="assets/css/style2.css<?php echo $_smarty_tpl->getValue('script_rev');?>
" rel="stylesheet">
        <?php 
$_smarty_tpl->getInheritance()->instanceBlock($_smarty_tpl, 'Block_1393045938686f4dece15864_12620590', "header");
?>


        <?php if ((true && ($_smarty_tpl->hasVariable('debugbarRenderer') && null !== ($_smarty_tpl->getValue('debugbarRenderer') ?? null)))) {?>
            <?php echo $_smarty_tpl->getValue('debugbarRenderer')->renderHead();?>

        <?php }?>
    </head>
    <body>
        
                <?php if ($_smarty_tpl->getValue('isLoggedIn')) {?>
            <nav class="navbar navbar-default">
                <div class="container-fluid">
                    <div class="navbar-header">
                        <a class="navbar-brand" href="/home"><?php echo $_smarty_tpl->getValue('currentBranch')['code'];?>
</a>
                    </div>
                    <div class="navbar-collapse">
                        <ul class="nav navbar-nav navbar-right">
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    <span class="glyphicon glyphicon-user"></span>
                                    <?php echo htmlspecialchars((string)(($tmp = $_smarty_tpl->getValue('currentUser')['name'] ?? null)===null||$tmp==='' ? $_smarty_tpl->getValue('currentUser')['username'] ?? null : $tmp), ENT_QUOTES, 'UTF-8', true);?>

                                    <span class="caret"></span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="/logout">Logout</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        <?php }?>

        <div class="container-fluid body-content">
            <?php 
$_smarty_tpl->getInheritance()->instanceBlock($_smarty_tpl, 'Block_1738514804686f4dece30037_77923074', "body");
?>

        </div>

        <?php echo '<script'; ?>
 src="assets/jquery-3.5.1.min.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/jquery-ui.min.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/bootstrap-3.4.1/js/bootstrap.min.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/bootstrap-submenu/js/bootstrap-submenu.min.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/bootstrap-modal/js/bootstrap-modalmanager.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/bootstrap-modal/js/bootstrap-modal.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/bootstrap-datetimepicker/js/moment.min.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/jquery.autocomplete.min.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/bootstrap-extends.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php echo '<script'; ?>
 src="assets/functions.js<?php echo $_smarty_tpl->getValue('script_rev');?>
"><?php echo '</script'; ?>
>
        <?php 
$_smarty_tpl->getInheritance()->instanceBlock($_smarty_tpl, 'Block_574839596686f4dece31aa4_74212240', "footer");
?>


        <?php if ((true && ($_smarty_tpl->hasVariable('debugbarRenderer') && null !== ($_smarty_tpl->getValue('debugbarRenderer') ?? null)))) {?>
            <?php echo $_smarty_tpl->getValue('debugbarRenderer')->render();?>

        <?php }?>
    </body>
</html>
<?php }
/* {block "header"} */
class Block_1393045938686f4dece15864_12620590 extends \Smarty\Runtime\Block
{
public function callBlock(\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates';
}
}
/* {/block "header"} */
/* {block "body"} */
class Block_1738514804686f4dece30037_77923074 extends \Smarty\Runtime\Block
{
public function callBlock(\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates';
}
}
/* {/block "body"} */
/* {block "footer"} */
class Block_574839596686f4dece31aa4_74212240 extends \Smarty\Runtime\Block
{
public function callBlock(\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates';
}
}
/* {/block "footer"} */
}
