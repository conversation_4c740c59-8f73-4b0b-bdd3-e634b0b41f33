<?php
session_start();
require_once __DIR__ . '/../vendor/autoload.php';

error_reporting(E_ALL ^ E_NOTICE);
if (config('app.display_error', false))
    ini_set("display_errors", 1);
else
    ini_set("display_errors", 0);

use App\Core\Router;
use App\Services\DebugBarService;
use App\Services\ErrorHandlerService;

// Initialize Debug Bar in development mode
$debugbar = DebugBarService::init();

// Set global exception handler
set_exception_handler(function ($exception) {
    ErrorHandlerService::handle($exception);
});

// Set global error handler for PHP errors
set_error_handler(function ($severity, $message, $file, $line) {
    // Convert PHP errors to exceptions
    if (error_reporting() & $severity) {
        throw new ErrorException($message, 0, $severity, $file, $line);
    }
});

try {
    // Dispatch based on current request
    Router::dispatch($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD']);
} catch (Throwable $exception) {
    // Handle any unhandled exceptions
    ErrorHandlerService::handle($exception);
}
