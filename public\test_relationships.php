<?php
/**
 * Test file for Model Relationships
 * 
 * This file demonstrates the new relationship functionality
 */

require_once '../vendor/autoload.php';
require_once '../src/helper.php';

use App\Models\User;
use App\Models\Branch;
use App\Models\UserPrivilege;
use App\Models\UserProfile;

// Set content type to HTML for better output formatting
header('Content-Type: text/html; charset=UTF-8');

// Simple styling
echo '<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #333; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    .success { color: green; }
    .error { color: red; }
    .example { background: #e9f7fe; padding: 15px; border-left: 4px solid #2196F3; margin-bottom: 20px; }
    .code { font-family: monospace; background: #f0f0f0; padding: 2px 4px; }
    .relationship { background: #f0f8ff; padding: 10px; border-left: 4px solid #4CAF50; margin: 10px 0; }
</style>';

echo '<h1>Model Relationships Test</h1>';

// Function to display test results
function displayRelationshipTest($title, $code, $result) {
    echo '<div class="example">';
    echo "<h3>{$title}</h3>";
    echo '<pre class="code">' . htmlspecialchars($code) . '</pre>';
    echo '<h4>Result:</h4>';
    
    if ($result === null) {
        echo '<p><em>No result (null)</em></p>';
    } elseif (is_array($result)) {
        if (empty($result)) {
            echo '<p><em>Empty array</em></p>';
        } else {
            echo '<p>Array with ' . count($result) . ' items:</p>';
            echo '<pre>';
            foreach (array_slice($result, 0, 3) as $index => $item) {
                if ($item instanceof App\Core\ModelInstance) {
                    echo "[$index] ModelInstance: " . json_encode($item->toArray()) . "\n";
                } else {
                    echo "[$index] " . print_r($item, true) . "\n";
                }
            }
            if (count($result) > 3) {
                echo "... and " . (count($result) - 3) . " more items\n";
            }
            echo '</pre>';
        }
    } elseif ($result instanceof App\Core\ModelInstance) {
        echo '<h5>ModelInstance Object:</h5>';
        echo '<pre>' . json_encode($result->toArray(), JSON_PRETTY_PRINT) . '</pre>';
    } else {
        echo '<pre>';
        print_r($result);
        echo '</pre>';
    }
    
    echo '</div>';
}

echo '<h2>Relationship Types</h2>';

echo '<div class="relationship">';
echo '<h3>Available Relationship Types</h3>';
echo '<ul>';
echo '<li><strong>hasOne</strong> - One-to-one relationship (User has one Profile)</li>';
echo '<li><strong>hasMany</strong> - One-to-many relationship (User has many Privileges)</li>';
echo '<li><strong>belongsTo</strong> - Inverse one-to-one/many relationship (Profile belongs to User)</li>';
echo '<li><strong>belongsToMany</strong> - Many-to-many relationship (User belongs to many Branches)</li>';
echo '</ul>';
echo '</div>';

echo '<h2>Testing Relationships</h2>';

// Test 1: Get a user and their relationships
try {
    $code = '$user = User::find(1);';
    $user = User::find(1);
    displayRelationshipTest('Find User', $code, $user);
    
    if ($user) {
        // Test hasMany relationship
        try {
            $code = '$user->privileges; // hasMany relationship';
            $privileges = $user->privileges;
            displayRelationshipTest('User Privileges (hasMany)', $code, $privileges);
        } catch (Exception $e) {
            echo '<div class="error">Error loading privileges: ' . $e->getMessage() . '</div>';
        }
        
        // Test belongsToMany relationship
        try {
            $code = '$user->branches; // belongsToMany relationship';
            $branches = $user->branches;
            displayRelationshipTest('User Branches (belongsToMany)', $code, $branches);
        } catch (Exception $e) {
            echo '<div class="error">Error loading branches: ' . $e->getMessage() . '</div>';
        }
        
        // Test hasOne relationship
        try {
            $code = '$user->profile; // hasOne relationship';
            $profile = $user->profile;
            displayRelationshipTest('User Profile (hasOne)', $code, $profile);
        } catch (Exception $e) {
            echo '<div class="error">Error loading profile: ' . $e->getMessage() . '</div>';
        }
    }
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 2: Get a branch and its users
try {
    $code = '$branch = Branch::find(1);';
    $branch = Branch::find(1);
    displayRelationshipTest('Find Branch', $code, $branch);
    
    if ($branch) {
        try {
            $code = '$branch->users; // belongsToMany relationship';
            $users = $branch->users;
            displayRelationshipTest('Branch Users (belongsToMany)', $code, $users);
        } catch (Exception $e) {
            echo '<div class="error">Error loading branch users: ' . $e->getMessage() . '</div>';
        }
    }
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

echo '<h2>Relationship Methods</h2>';

echo '<div class="example">';
echo '<h3>Loading Relationships</h3>';
echo '<pre class="code">// Lazy loading (automatic)
$user = User::find(1);
$privileges = $user->privileges; // Loads when accessed

// Explicit loading
$user->load("privileges");
$user->loadRelations(["privileges", "branches"]);

// Check if loaded
if ($user->relationLoaded("privileges")) {
    echo "Privileges are loaded";
}</pre>';
echo '</div>';

echo '<div class="example">';
echo '<h3>Working with Relationships</h3>';
echo '<pre class="code">// Get relationship instance
$relationship = $user->getRelationship("privileges");

// Count related items
$count = $relationship->count();

// Create related items
$newPrivilege = $relationship->create([
    "privilege_id" => 1,
    "branch_id" => 1
]);

// For belongsTo relationships
$profile = UserProfile::find(1);
$user = $profile->user; // Gets the related user

// For many-to-many relationships
$user = User::find(1);
$branch = Branch::find(2);

// Attach/detach (for belongsToMany)
$userBranchRelation = $user->getRelationship("branches");
$userBranchRelation->attach(2); // Attach branch ID 2
$userBranchRelation->detach(2); // Detach branch ID 2</pre>';
echo '</div>';

echo '<h2>Advanced Usage</h2>';

echo '<div class="example">';
echo '<h3>Chaining and Filtering</h3>';
echo '<pre class="code">// Get user with relationships
$user = User::find(1);

// Access nested relationships
foreach ($user->privileges as $privilege) {
    echo $privilege->branch->name; // Chain relationships
}

// Count relationships
$privilegeCount = $user->getRelationship("privileges")->count();
echo "User has {$privilegeCount} privileges";

// Create related records
$user->getRelationship("privileges")->create([
    "privilege_id" => 5,
    "branch_id" => 1
]);</pre>';
echo '</div>';

echo '<h2>Relationship Definitions</h2>';

echo '<div class="example">';
echo '<h3>How Relationships are Defined</h3>';
echo '<pre class="code">// In User model
public static function privileges()
{
    return static::hasMany(UserPrivilege::class, "user_id", "id");
}

public static function branches()
{
    return static::belongsToMany(Branch::class, "user_branch", "user_id", "branch_id");
}

public static function profile()
{
    return static::hasOne(UserProfile::class, "user_id", "id");
}

// In UserPrivilege model
public static function user()
{
    return static::belongsTo(User::class, "user_id", "id");
}

public static function branch()
{
    return static::belongsTo(Branch::class, "branch_id", "id");
}

// In Branch model
public static function users()
{
    return static::belongsToMany(User::class, "user_branch", "branch_id", "user_id");
}</pre>';
echo '</div>';

echo '<h2>Benefits</h2>';
echo '<ul>';
echo '<li><strong>Lazy Loading</strong> - Relationships load only when accessed</li>';
echo '<li><strong>Automatic Queries</strong> - No need to write JOIN queries manually</li>';
echo '<li><strong>Object-Oriented</strong> - Work with related objects naturally</li>';
echo '<li><strong>Performance</strong> - Efficient queries with proper indexing</li>';
echo '<li><strong>Maintainable</strong> - Clear relationship definitions in models</li>';
echo '</ul>';

echo '<p class="success">Relationship testing completed!</p>';

echo '<h2>Next Steps</h2>';
echo '<ul>';
echo '<li>Define relationships in your models using hasOne, hasMany, belongsTo, belongsToMany</li>';
echo '<li>Access relationships as properties: <code>$user->privileges</code></li>';
echo '<li>Use relationship methods for advanced operations</li>';
echo '<li>Create pivot tables for many-to-many relationships</li>';
echo '</ul>';
?>
