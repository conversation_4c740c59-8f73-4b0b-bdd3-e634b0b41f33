<?php

namespace App\Core;

/**
 * Model Instance Class
 *
 * Represents a single model record as an object with dynamic properties
 * and helper methods for common operations.
 */
class ModelInstance
{
    /** @var array */
    protected $attributes = [];

    /** @var string */
    protected $modelClass;

    /** @var string */
    protected $table;

    /** @var string */
    protected $primaryKey;

    /** @var bool */
    protected $exists = false;

    /** @var array */
    protected $relations = [];

    /**
     * ModelInstance constructor
     *
     * @param array $attributes Record attributes
     * @param string $modelClass The model class name
     * @param string $table Table name
     * @param string $primaryKey Primary key column name
     * @param bool $exists Whether this record exists in database
     */
    public function __construct(array $attributes = [], string $modelClass = '', string $table = '', string $primaryKey = 'id', bool $exists = false)
    {
        $this->attributes = $attributes;
        $this->modelClass = $modelClass;
        $this->table = $table;
        $this->primaryKey = $primaryKey;
        $this->exists = $exists;
    }

    /**
     * Get an attribute value or relationship
     *
     * @param string $key Attribute name or relationship name
     * @return mixed Attribute value, relationship result, or null if not found
     */
    public function __get(string $key)
    {
        // Check if it's a regular attribute first
        if (array_key_exists($key, $this->attributes)) {
            return $this->attributes[$key];
        }

        // Check if it's a loaded relationship
        if (array_key_exists($key, $this->relations)) {
            return $this->relations[$key];
        }

        // Try to load relationship dynamically
        if ($this->modelClass && method_exists($this->modelClass, $key)) {
            $relationship = call_user_func([$this->modelClass, $key]);
            if ($relationship instanceof Relationship) {
                $result = $relationship->setParent($this)->get();
                $this->relations[$key] = $result;
                return $result;
            }
        }

        return null;
    }

    /**
     * Set an attribute value
     *
     * @param string $key Attribute name
     * @param mixed $value Attribute value
     */
    public function __set(string $key, $value)
    {
        $this->attributes[$key] = $value;
    }

    /**
     * Check if an attribute exists
     *
     * @param string $key Attribute name
     * @return bool True if attribute exists
     */
    public function __isset(string $key): bool
    {
        return isset($this->attributes[$key]);
    }

    /**
     * Unset an attribute
     *
     * @param string $key Attribute name
     */
    public function __unset(string $key)
    {
        unset($this->attributes[$key]);
    }

    /**
     * Get all attributes as array
     *
     * @return array All attributes
     */
    public function toArray(): array
    {
        return $this->attributes;
    }

    /**
     * Get attributes as JSON string
     *
     * @return string JSON representation
     */
    public function toJson(): string
    {
        return json_encode($this->attributes);
    }

    /**
     * Get the primary key value
     *
     * @return mixed Primary key value
     */
    public function getKey()
    {
        return $this->attributes[$this->primaryKey] ?? null;
    }

    /**
     * Check if the model exists in database
     *
     * @return bool True if exists in database
     */
    public function exists(): bool
    {
        return $this->exists;
    }

    /**
     * Save the model to database
     *
     * @return bool True if save successful
     * @throws \Exception If model class not set or database operation fails
     */
    public function save(): bool
    {
        if (!$this->modelClass) {
            throw new \Exception("Model class not set for instance");
        }

        $modelClass = $this->modelClass;

        if ($this->exists && $this->getKey()) {
            // Update existing record
            $result = $modelClass::updateById($this->getKey(), $this->attributes);
            return $result;
        } else {
            // Create new record
            $result = $modelClass::create($this->attributes);
            if ($result) {
                $this->exists = true;
                // Get the last inserted ID if available
                $lastId = DB()->lastInsertId();
                if ($lastId) {
                    $this->attributes[$this->primaryKey] = $lastId;
                }
            }
            return $result;
        }
    }

    /**
     * Delete the model from database
     *
     * @return bool True if deletion successful
     * @throws \Exception If model class not set or no primary key
     */
    public function delete(): bool
    {
        if (!$this->modelClass) {
            throw new \Exception("Model class not set for instance");
        }

        $key = $this->getKey();
        if (!$key) {
            throw new \Exception("Cannot delete model without primary key");
        }

        $modelClass = $this->modelClass;
        $result = $modelClass::destroy($key);
        
        if ($result) {
            $this->exists = false;
        }
        
        return $result;
    }

    /**
     * Refresh the model from database
     *
     * @return bool True if refresh successful
     * @throws \Exception If model class not set or no primary key
     */
    public function refresh(): bool
    {
        if (!$this->modelClass) {
            throw new \Exception("Model class not set for instance");
        }

        $key = $this->getKey();
        if (!$key) {
            throw new \Exception("Cannot refresh model without primary key");
        }

        $modelClass = $this->modelClass;
        $fresh = $modelClass::find($key);
        
        if ($fresh) {
            $this->attributes = $fresh->toArray();
            return true;
        }
        
        return false;
    }

    /**
     * Update specific attributes
     *
     * @param array $attributes Attributes to update
     * @return bool True if update successful
     */
    public function update(array $attributes): bool
    {
        foreach ($attributes as $key => $value) {
            $this->attributes[$key] = $value;
        }

        return $this->save();
    }

    /**
     * Get a specific attribute
     *
     * @param string $key Attribute name
     * @param mixed $default Default value if not found
     * @return mixed Attribute value or default
     */
    public function getAttribute(string $key, $default = null)
    {
        return $this->attributes[$key] ?? $default;
    }

    /**
     * Set a specific attribute
     *
     * @param string $key Attribute name
     * @param mixed $value Attribute value
     * @return self For method chaining
     */
    public function setAttribute(string $key, $value): self
    {
        $this->attributes[$key] = $value;
        return $this;
    }

    /**
     * Check if attribute has a value (not null or empty)
     *
     * @param string $key Attribute name
     * @return bool True if has value
     */
    public function hasAttribute(string $key): bool
    {
        return isset($this->attributes[$key]) && $this->attributes[$key] !== null && $this->attributes[$key] !== '';
    }

    /**
     * Convert to string (JSON representation)
     *
     * @return string JSON string
     */
    public function __toString(): string
    {
        return $this->toJson();
    }

    /**
     * Debug info for var_dump
     *
     * @return array Debug information
     */
    public function __debugInfo(): array
    {
        return [
            'attributes' => $this->attributes,
            'relations' => $this->relations,
            'exists' => $this->exists,
            'table' => $this->table,
            'primaryKey' => $this->primaryKey
        ];
    }

    // ===== RELATIONSHIP METHODS =====

    /**
     * Load a relationship
     *
     * @param string $relation Relationship name
     * @return mixed Relationship result
     * @throws \Exception If relationship doesn't exist
     */
    public function load(string $relation)
    {
        if (!$this->modelClass || !method_exists($this->modelClass, $relation)) {
            throw new \Exception("Relationship '{$relation}' not found on model {$this->modelClass}");
        }

        $relationship = call_user_func([$this->modelClass, $relation]);
        if (!$relationship instanceof Relationship) {
            throw new \Exception("Method '{$relation}' is not a relationship");
        }

        $result = $relationship->setParent($this)->get();
        $this->relations[$relation] = $result;

        return $result;
    }

    /**
     * Load multiple relationships
     *
     * @param array $relations Array of relationship names
     * @return self For method chaining
     */
    public function loadRelations(array $relations): self
    {
        foreach ($relations as $relation) {
            $this->load($relation);
        }

        return $this;
    }

    /**
     * Check if a relationship is loaded
     *
     * @param string $relation Relationship name
     * @return bool True if loaded
     */
    public function relationLoaded(string $relation): bool
    {
        return array_key_exists($relation, $this->relations);
    }

    /**
     * Get a relationship instance without loading it
     *
     * @param string $relation Relationship name
     * @return Relationship
     * @throws \Exception If relationship doesn't exist
     */
    public function getRelationship(string $relation): Relationship
    {
        if (!$this->modelClass || !method_exists($this->modelClass, $relation)) {
            throw new \Exception("Relationship '{$relation}' not found on model {$this->modelClass}");
        }

        $relationship = call_user_func([$this->modelClass, $relation]);
        if (!$relationship instanceof Relationship) {
            throw new \Exception("Method '{$relation}' is not a relationship");
        }

        return $relationship->setParent($this);
    }

    /**
     * Get all loaded relations
     *
     * @return array Loaded relations
     */
    public function getRelations(): array
    {
        return $this->relations;
    }

    /**
     * Set a relation value
     *
     * @param string $relation Relationship name
     * @param mixed $value Relationship value
     * @return self For method chaining
     */
    public function setRelation(string $relation, $value): self
    {
        $this->relations[$relation] = $value;
        return $this;
    }

    /**
     * Unset a loaded relation
     *
     * @param string $relation Relationship name
     * @return self For method chaining
     */
    public function unsetRelation(string $relation): self
    {
        unset($this->relations[$relation]);
        return $this;
    }
}
