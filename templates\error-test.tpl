{extends file="base.tpl"}

{block name="title"}Error <PERSON>ling Test Page{/block}

{block name="body"}
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h1>HTTP Error Handling Test Page</h1>
                <p class="lead">Test the improved HTTP error handling system</p>
            </div>

            <div class="alert alert-warning">
                <strong>Note:</strong> This page is only available in debug mode for testing purposes.
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">HTTP Error Tests</h3>
                        </div>
                        <div class="panel-body">
                            <div class="btn-group-vertical btn-block">
                                <a href="/error-test/test404" class="btn btn-danger">
                                    <i class="fa fa-exclamation-triangle"></i> Test 404 Not Found
                                </a>
                                <a href="/error-test/test403" class="btn btn-warning">
                                    <i class="fa fa-ban"></i> Test 403 Forbidden
                                </a>
                                <a href="/error-test/test401" class="btn btn-info">
                                    <i class="fa fa-lock"></i> Test 401 Unauthorized
                                </a>
                                <a href="/error-test/test500" class="btn btn-danger">
                                    <i class="fa fa-fire"></i> Test 500 Internal Server Error
                                </a>
                                <a href="/error-test/testValidation" class="btn btn-warning">
                                    <i class="fa fa-check-circle"></i> Test Validation Error
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">JSON Response Tests</h3>
                        </div>
                        <div class="panel-body">
                            <div class="btn-group-vertical btn-block">
                                <button onclick="testJsonError()" class="btn btn-danger">
                                    <i class="fa fa-code"></i> Test JSON Error Response
                                </button>
                                <button onclick="testJsonSuccess()" class="btn btn-success">
                                    <i class="fa fa-check"></i> Test JSON Success Response
                                </button>
                                <a href="/error-test/testException" class="btn btn-danger">
                                    <i class="fa fa-bug"></i> Test Unhandled Exception
                                </a>
                                <a href="/error-test/testDatabaseError" class="btn btn-warning">
                                    <i class="fa fa-database"></i> Test Database Error
                                </a>
                                <a href="/error-test/testConditionalAbort?trigger=1" class="btn btn-info">
                                    <i class="fa fa-question-circle"></i> Test Conditional Abort
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h3 class="panel-title">JSON Response Output</h3>
                        </div>
                        <div class="panel-body">
                            <pre id="json-output" style="background: #f8f9fa; padding: 15px; border-radius: 5px; min-height: 100px;">
Click a JSON test button to see the response here...
                            </pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">Features Implemented</h3>
                        </div>
                        <div class="panel-body">
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Custom HTTP exception classes for different status codes
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Centralized error handler service with logging integration
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Helper functions for consistent error responses
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Error page templates for common HTTP errors
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Enhanced Router with proper exception handling
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Global exception handler for unhandled errors
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Enhanced Controller base class with error methods
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Improved middleware error handling
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Auto-detection of API vs web requests
                                </li>
                                <li class="list-group-item">
                                    <i class="fa fa-check text-success"></i>
                                    Debug information in development mode
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testJsonError() {
    fetch('/error-test/testJsonError', {
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('json-output').textContent = JSON.stringify(data, null, 2);
    })
    .catch(error => {
        document.getElementById('json-output').textContent = 'Error: ' + error.message;
    });
}

function testJsonSuccess() {
    fetch('/error-test/testJsonSuccess', {
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('json-output').textContent = JSON.stringify(data, null, 2);
    })
    .catch(error => {
        document.getElementById('json-output').textContent = 'Error: ' + error.message;
    });
}
</script>
{/block}
