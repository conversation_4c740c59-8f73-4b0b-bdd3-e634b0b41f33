{* Branch Switcher Component *}
<div class="branch-switcher">
    <div class="current-branch">
        <span class="branch-label">Current Branch:</span>
        <span class="branch-code" id="current-branch-code">
            {if isset($current_branch)}
                {$current_branch.code}
            {else}
                No branch selected
            {/if}
        </span>
        <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleBRanchSelector()">
            <i class="fa fa-globe"></i> Switch Branch
        </button>
    </div>
    
    <div class="branch-selector" id="branch-selector" style="display: none;">
        <form id="branch-switch-form" method="POST" action="/branches/switch">
            <div class="form-group">
                <label for="switch-branch-id">Select BRanch:</label>
                <select class="form-control" name="branch_id" id="switch-branch-id" required>
                    <option value="">Choose branch...</option>
                    {if isset($available_branches)}
                        {foreach from=$available_branches item=branch}
                            <option value="{$branch.id}" 
                                    {if isset($current_branch) && $current_branch.id == $branch.id}selected{/if}>
                                        {$branch.code}                                
                            </option>
                        {/foreach}
                    {/if}
                </select>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary btn-sm">Switch</button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="toggleBranchSelector()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<style>
.branch-switcher {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 20px;
}

.current-branch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.branch-label {
    font-weight: bold;
    color: #495057;
}

.branch-code {
    color: #007bff;
    font-weight: 500;
}

.branch-selector {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.form-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-outline-primary {
    color: #007bff;
    border-color: #007bff;
    background: transparent;
}

.btn-outline-primary:hover {
    background: #007bff;
    color: white;
}

@media (max-width: 768px) {
    .current-branch {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>

<script>
function toggleBRanchSelector() {
    const selector = document.getElementById('branch-selector');
    if (selector.style.display === 'none') {
        selector.style.display = 'block';
        loadAvailableBranches();
    } else {
        selector.style.display = 'none';
    }
}

function loadAvailableBranches() {
    fetch('/branches/available')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('switch-branch-id');
            select.innerHTML = '<option value="">Choose branch...</option>';
            
            data.branches.forEach(branch => {
                const option = document.createElement('option');
                option.value = branch.id;
                option.textContent = `${branch.code}`;
                
                if (data.current_branch && data.current_branch.id == branch.id) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading branches:', error);
        });
}

// Handle form submission with AJAX
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('branch-switch-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            
            fetch('/branchs/switch', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update current branch display
                    document.getElementById('current-branch-code').textContent = 
                        `${data.branch.code}`;
                    
                    // Hide selector
                    toggleBranchSelector();
                    
                    // Show success message
                    showMessage('Branch switched successfully!', 'success');
                    
                    // Optionally reload page to reflect changes
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showMessage(data.error || 'Failed to switch branch', 'error');
                }
            })
            .catch(error => {
                console.error('Error switching branch:', error);
                showMessage('Error switching branch', 'error');
            });
        });
    }
});

function showMessage(message, type) {
    // Create or update message element
    let messageEl = document.getElementById('branch-message');
    if (!messageEl) {
        messageEl = document.createElement('div');
        messageEl.id = 'branch-message';
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 1000;
            max-width: 300px;
        `;
        document.body.appendChild(messageEl);
    }
    
    messageEl.textContent = message;
    messageEl.className = type === 'success' ? 'alert alert-success' : 'alert alert-danger';
    messageEl.style.display = 'block';
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
        messageEl.style.display = 'none';
    }, 3000);
}
</script>
