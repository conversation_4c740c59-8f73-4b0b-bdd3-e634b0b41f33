/*!
 * Bootstrap-submenu v2.0.4 (https://vsn4ik.github.io/bootstrap-submenu/)
 * Copyright 2014-2016 Vasily A. (https://github.com/vsn4ik)
 * Licensed under the MIT license
 */

.dropdown-submenu > a:after {
  content: "";
}
@media (min-width: 768px) {
  .dropdown-submenu {
    position: relative;
  }
  .dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    border-top-left-radius: 0;
  }
  .dropup .dropdown-submenu .dropdown-menu,
  .navbar-fixed-bottom .dropdown-submenu .dropdown-menu {
    top: auto;
    bottom: 0;
    margin-top: 0;
    margin-bottom: -6px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 0;
  }
  .dropdown-menu-right .dropdown-submenu .dropdown-menu,
  .navbar-right .dropdown-submenu .dropdown-menu {
    left: auto;
    right: 100%;
    border-top-left-radius: 4px;
    border-top-right-radius: 0;
  }
  .dropup .dropdown-menu-right .dropdown-submenu .dropdown-menu,
  .dropup .navbar-right .dropdown-submenu .dropdown-menu,
  .navbar-fixed-bottom .dropdown-menu-right .dropdown-submenu .dropdown-menu,
  .navbar-fixed-bottom .navbar-right .dropdown-submenu .dropdown-menu {
    border-radius: 4px 4px 0;
  }
  .dropdown-submenu > a:after {
    float: right;
    margin-top: 6px;
    margin-right: -10px;
    border-left: 4px dashed;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
  }
  .dropdown-menu-right .dropdown-submenu > a:after,
  .navbar-right .dropdown-submenu > a:after {
    float: left;
    border-left: none;
    margin-left: -10px;
    margin-right: 0;
    border-right: 4px dashed;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
  }
}
@media (max-width: 767px) {
  .dropdown-submenu .dropdown-menu {
    position: static;
    margin-top: 0;
    border: 0;
    box-shadow: none;
  }
  .dropdown-submenu > a:after {
    margin-left: 6px;
    display: inline-block;
    vertical-align: middle;
    border-top: 4px dashed;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
  }
  .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li.dropdown-header,
  .dropup > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li.dropdown-header,
  .btn-group > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li.dropdown-header,
  .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > a,
  .dropup > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > a,
  .btn-group > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > a {
    padding-left: 30px;
  }
  .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .dropup > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .btn-group > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > a,
  .dropup > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > a,
  .btn-group > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > a {
    padding-left: 40px;
  }
  .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .dropup > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .btn-group > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > a,
  .dropup > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > a,
  .btn-group > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > a {
    padding-left: 50px;
  }
  .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .dropup > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .btn-group > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > a,
  .dropup > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > a,
  .btn-group > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > a {
    padding-left: 60px;
  }
  .navbar-nav > .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li.dropdown-header,
  .navbar-nav > .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > a {
    padding-left: 35px;
  }
  .navbar-nav > .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .navbar-nav > .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > a {
    padding-left: 45px;
  }
  .navbar-nav > .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .navbar-nav > .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > a {
    padding-left: 55px;
  }
  .navbar-nav > .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li.dropdown-header,
  .navbar-nav > .dropdown > .dropdown-menu > .dropdown-submenu > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > .dropdown-menu > li > a {
    padding-left: 65px;
  }
}
/*# sourceMappingURL=bootstrap-submenu.css.map */