<?php

namespace App\Core;

use App\Exceptions\NotFoundException;

class App
{
    protected string $controller = 'HomeController';
    protected string $method = 'index';
    protected array $params = [];

    public function __construct()
    {
        $url = $this->parseUrl();

        $controllerClass = "App\\Controllers\\" . ucfirst($url[0]) . "Controller";

        if (class_exists($controllerClass)) {
            $this->controller = $controllerClass;
            unset($url[0]);
        } else {
            throw new NotFoundException("Controller not found.");
        }

        $controllerInstance = new $this->controller;

        if (isset($url[1]) && method_exists($controllerInstance, $url[1])) {
            $this->method = $url[1];
            unset($url[1]);
        }

        $this->params = array_values($url ?? []);

        call_user_func_array([$controllerInstance, $this->method], $this->params);
    }

    private function parseUrl(): array
    {
        if (isset($_GET['url'])) {
            return explode(
                "/",
                filter_var(rtrim($_GET['url'], "/"), FILTER_SANITIZE_URL)
            );
        }

        return ['home'];
    }
}
