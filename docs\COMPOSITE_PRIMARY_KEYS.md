# Composite Primary Keys

This document explains the composite primary key functionality added to the models, allowing tables to use multiple columns as their primary key.

## Overview

Composite primary keys allow you to use multiple columns together as the primary key for a table. This is useful for:
- Pivot tables in many-to-many relationships
- Junction tables that connect multiple entities
- Tables where the natural key consists of multiple fields
- Time-based data where date/time is part of the key

## Defining Composite Primary Keys

### Basic Definition

Instead of a single string, define the primary key as an array:

```php
class UserPrivilege extends Model
{
    protected static $table = 'user_privilege';
    
    // Composite primary key with 3 columns
    protected static $primaryKey = ['user_id', 'branch_id', 'privilege'];
    
    protected static $fillable = ['user_id', 'branch_id', 'privilege'];
    protected static $guarded = [];
    protected static $timestamps = false;
}
```

### Two-Column Composite Key

```php
class UserBranch extends Model
{
    protected static $table = 'user_branch';
    
    // Composite primary key with 2 columns
    protected static $primaryKey = ['user_id', 'branch_id'];
    
    protected static $fillable = ['user_id', 'branch_id', 'role', 'access_level'];
    protected static $timestamps = true;
}
```

## Usage Examples

### Finding Records

#### Single Record by Composite Key

```php
// Find by providing array of key values in order
$privilege = UserPrivilege::find([1, 2, 'READ']);
// Finds record where user_id=1, branch_id=2, privilege='READ'

$userBranch = UserBranch::find([1, 2]);
// Finds record where user_id=1, branch_id=2
```

#### Multiple Records by Composite Keys

```php
// Find multiple records with composite keys
$privileges = UserPrivilege::findMany([
    [1, 2, 'READ'],
    [1, 2, 'WRITE'],
    [1, 3, 'ADMIN']
]);

$userBranches = UserBranch::findMany([
    [1, 2],
    [1, 3],
    [2, 2]
]);
```

### Creating Records

```php
// Create with composite key
$privilege = UserPrivilege::create([
    'user_id' => 1,
    'branch_id' => 2,
    'privilege' => 'WRITE'
]);

$userBranch = UserBranch::create([
    'user_id' => 1,
    'branch_id' => 3,
    'role' => 'manager',
    'access_level' => 'full'
]);
```

### Updating Records

```php
// Find and update
$privilege = UserPrivilege::find([1, 2, 'READ']);
if ($privilege) {
    // Update using object methods
    $privilege->save(); // Uses composite key for WHERE clause
}

// Direct update by composite key
UserPrivilege::updateById([1, 2, 'READ'], [
    'updated_at' => date('Y-m-d H:i:s')
]);
```

### Deleting Records

```php
// Delete using object method
$privilege = UserPrivilege::find([1, 2, 'READ']);
$privilege->delete();

// Direct delete by composite key
UserPrivilege::destroy([1, 2, 'READ']);

// Delete multiple
UserPrivilege::destroy([1, 2, 'WRITE']);
UserPrivilege::destroy([1, 3, 'ADMIN']);
```

## Working with Composite Key Values

### Getting Primary Key Values

```php
$privilege = UserPrivilege::find([1, 2, 'READ']);

// Get composite key as array
$key = $privilege->getKey(); // Returns: [1, 2, 'READ']

// Check if model uses composite key
if ($privilege->hasCompositePrimaryKey()) {
    echo "This model uses composite primary key";
}

// Get primary key column names
$columns = $privilege->getPrimaryKeyColumns(); 
// Returns: ['user_id', 'branch_id', 'privilege']
```

### Validation

```php
$privilege = UserPrivilege::find([1, 2, 'READ']);

// Check if has valid primary key (all parts non-null)
if ($privilege->hasValidPrimaryKey()) {
    echo "Valid composite key";
}
```

## Custom Methods for Composite Key Models

### UserBranch Example

```php
class UserBranch extends Model
{
    protected static $primaryKey = ['user_id', 'branch_id'];
    
    // Custom finder methods
    public static function findByUserAndBranch(int $userId, int $branchId)
    {
        return static::find([$userId, $branchId]);
    }
    
    public static function forUser(int $userId): array
    {
        $records = static::where('user_id', $userId)->get();
        return static::newCollection($records);
    }
    
    public static function forBranch(int $branchId): array
    {
        $records = static::where('branch_id', $branchId)->get();
        return static::newCollection($records);
    }
    
    // Upsert operation
    public static function createOrUpdate(int $userId, int $branchId, array $attributes = [])
    {
        $existing = static::findByUserAndBranch($userId, $branchId);
        
        if ($existing) {
            $existing->update($attributes);
            return $existing;
        } else {
            $data = array_merge(['user_id' => $userId, 'branch_id' => $branchId], $attributes);
            return static::create($data);
        }
    }
    
    // Remove relationship
    public static function removeRelationship(int $userId, int $branchId): bool
    {
        return static::destroy([$userId, $branchId]);
    }
}
```

### Usage of Custom Methods

```php
// Find specific relationship
$userBranch = UserBranch::findByUserAndBranch(1, 2);

// Get all branches for a user
$userBranches = UserBranch::forUser(1);

// Get all users for a branch
$branchUsers = UserBranch::forBranch(2);

// Create or update relationship
$userBranch = UserBranch::createOrUpdate(1, 2, [
    'role' => 'admin',
    'access_level' => 'full'
]);

// Remove relationship
UserBranch::removeRelationship(1, 2);
```

## Database Schema

### Required Table Structure

```sql
-- UserPrivilege table with 3-column composite key
CREATE TABLE user_privilege (
    user_id INT NOT NULL,
    branch_id INT NOT NULL,
    privilege VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, branch_id, privilege),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    INDEX idx_user_privilege_user (user_id),
    INDEX idx_user_privilege_branch (branch_id)
);

-- UserBranch table with 2-column composite key
CREATE TABLE user_branch (
    user_id INT NOT NULL,
    branch_id INT NOT NULL,
    role VARCHAR(50),
    access_level VARCHAR(50),
    assigned_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, branch_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    INDEX idx_user_branch_user (user_id),
    INDEX idx_user_branch_branch (branch_id)
);
```

### Indexing Strategy

```sql
-- Primary key automatically creates index
-- Additional indexes for foreign keys
CREATE INDEX idx_user_privilege_user_id ON user_privilege(user_id);
CREATE INDEX idx_user_privilege_branch_id ON user_privilege(branch_id);

-- Partial indexes for common queries
CREATE INDEX idx_user_privilege_user_branch ON user_privilege(user_id, branch_id);

-- Covering indexes for performance
CREATE INDEX idx_user_branch_user_role ON user_branch(user_id, role);
```

## Relationships with Composite Keys

### Defining Relationships

```php
class UserPrivilege extends Model
{
    protected static $primaryKey = ['user_id', 'branch_id', 'privilege'];
    
    public static function user()
    {
        return static::belongsTo(User::class, 'user_id', 'id');
    }
    
    public static function branch()
    {
        return static::belongsTo(Branch::class, 'branch_id', 'id');
    }
}

class User extends Model
{
    public static function privileges()
    {
        return static::hasMany(UserPrivilege::class, 'user_id', 'id');
    }
    
    public static function userBranches()
    {
        return static::hasMany(UserBranch::class, 'user_id', 'id');
    }
}
```

### Using Relationships

```php
$user = User::find(1);

// Get user's privileges
$privileges = $user->privileges;
foreach ($privileges as $privilege) {
    echo "User has {$privilege->privilege} access to branch {$privilege->branch_id}";
}

// Get user's branch relationships
$userBranches = $user->userBranches;
foreach ($userBranches as $userBranch) {
    echo "User has {$userBranch->role} role in branch {$userBranch->branch_id}";
}
```

## Performance Considerations

### Query Optimization

```php
// Efficient: Use composite key directly
$privilege = UserPrivilege::find([1, 2, 'READ']);

// Less efficient: Multiple where clauses
$privilege = UserPrivilege::where('user_id', 1)
    ->where('branch_id', 2)
    ->where('privilege', 'READ')
    ->first();
```

### Batch Operations

```php
// Efficient: Batch find
$privileges = UserPrivilege::findMany([
    [1, 2, 'READ'],
    [1, 2, 'WRITE'],
    [1, 3, 'ADMIN']
]);

// Less efficient: Individual finds
$privileges = [];
$privileges[] = UserPrivilege::find([1, 2, 'READ']);
$privileges[] = UserPrivilege::find([1, 2, 'WRITE']);
$privileges[] = UserPrivilege::find([1, 3, 'ADMIN']);
```

## Best Practices

### 1. Column Order Matters

```php
// Define primary key columns in logical order
protected static $primaryKey = ['user_id', 'branch_id', 'privilege'];

// Use same order when calling methods
$privilege = UserPrivilege::find([1, 2, 'READ']); // user_id, branch_id, privilege
```

### 2. Validation

```php
public static function create(array $data)
{
    // Validate required composite key fields
    $required = ['user_id', 'branch_id', 'privilege'];
    foreach ($required as $field) {
        if (!isset($data[$field])) {
            throw new \Exception("Required field {$field} is missing");
        }
    }
    
    return parent::create($data);
}
```

### 3. Custom Accessors

```php
public function getCompositeKeyString(): string
{
    $key = $this->getKey();
    return implode('-', $key); // "1-2-READ"
}

public function getKeyHash(): string
{
    return md5(json_encode($this->getKey()));
}
```

## Error Handling

```php
try {
    // This will throw exception if wrong number of key values
    $privilege = UserPrivilege::find([1, 2]); // Missing third key value
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

// Safe access
$key = [1, 2, 'READ'];
if (count($key) === 3) {
    $privilege = UserPrivilege::find($key);
}
```

## Testing

Test composite key functionality using the provided test page:
- Visit `/test_composite_keys.php` in your browser
- See live examples of all composite key operations
- Test with your actual database data
- Verify key validation and error handling

## Migration from Single Keys

### Before (Single Primary Key)

```php
class UserPrivilege extends Model
{
    protected static $primaryKey = 'id';
    
    // Required unique constraint
    // UNIQUE KEY unique_user_branch_privilege (user_id, branch_id, privilege)
}

// Usage
$privilege = UserPrivilege::find(123);
```

### After (Composite Primary Key)

```php
class UserPrivilege extends Model
{
    protected static $primaryKey = ['user_id', 'branch_id', 'privilege'];
    
    // No need for separate ID column or unique constraint
}

// Usage
$privilege = UserPrivilege::find([1, 2, 'READ']);
```

The composite primary key system provides a natural, efficient way to work with tables that have multi-column primary keys while maintaining the same elegant API as single-key models.
