{extends file="../base.tpl"}

{block name="title"}500 - Internal Server Error{/block}

{block name="body"}
<div class="container">
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="error-page text-center">
                <div class="error-code">
                    <h1 style="font-size: 120px; color: #dc3545; margin: 0;">500</h1>
                </div>
                
                <div class="error-content">
                    <h2 class="error-title" style="margin: 20px 0;">Internal Server Error</h2>
                    
                    <p class="error-message" style="font-size: 18px; color: #666; margin: 20px 0;">
                        {if $error_message}
                            {$error_message}
                        {else}
                            Something went wrong on our end. We're working to fix it.
                        {/if}
                    </p>
                    
                    <div class="error-actions" style="margin: 30px 0;">
                        <a href="/" class="btn btn-primary btn-lg">
                            <i class="fa fa-home"></i> Go Home
                        </a>
                        <button onclick="location.reload()" class="btn btn-warning btn-lg">
                            <i class="fa fa-refresh"></i> Try Again
                        </button>
                        <button onclick="history.back()" class="btn btn-default btn-lg">
                            <i class="fa fa-arrow-left"></i> Go Back
                        </button>
                    </div>
                </div>
                
                {if {config key='app.debug' default=false} && $debug_info}
                <div class="debug-info" style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 30px; text-align: left;">
                    <h4>Debug Information</h4>
                    <p><strong>File:</strong> {$debug_info.file}</p>
                    <p><strong>Line:</strong> {$debug_info.line}</p>
                    <details>
                        <summary><strong>Stack Trace</strong></summary>
                        <pre style="white-space: pre-wrap; font-size: 12px;">{$debug_info.trace}</pre>
                    </details>
                </div>
                {/if}
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 60px 0;
}

.error-code h1 {
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-title {
    color: #333;
    font-weight: 300;
}

.error-message {
    line-height: 1.6;
}

.error-actions .btn {
    margin: 0 10px;
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 80px;
    }
    
    .error-actions .btn {
        display: block;
        margin: 10px auto;
        width: 200px;
    }
}
</style>
{/block}
