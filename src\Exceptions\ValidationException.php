<?php

namespace App\Exceptions;

/**
 * 422 Unprocessable Entity Exception
 * 
 * Thrown when request data validation fails.
 */
class ValidationException extends HttpException
{
    protected array $validationErrors;

    public function __construct(
        array $validationErrors = [],
        string $message = 'The request data could not be processed.',
        array $errorData = [],
        array $headers = []
    ) {
        $this->validationErrors = $validationErrors;
        
        // Add validation errors to error data
        $errorData['validation_errors'] = $validationErrors;
        
        parent::__construct(422, $message, $errorData, $headers);
    }

    /**
     * Get validation errors
     */
    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }

    /**
     * Check if there are validation errors for a specific field
     */
    public function hasError(string $field): bool
    {
        return isset($this->validationErrors[$field]);
    }

    /**
     * Get validation errors for a specific field
     */
    public function getFieldErrors(string $field): array
    {
        return $this->validationErrors[$field] ?? [];
    }
}
