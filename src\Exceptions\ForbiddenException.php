<?php

namespace App\Exceptions;

/**
 * 403 Forbidden Exception
 * 
 * Thrown when access to a resource is forbidden.
 */
class ForbiddenException extends HttpException
{
    public function __construct(
        string $message = 'You do not have permission to access this resource.',
        array $errorData = [],
        array $headers = []
    ) {
        parent::__construct(403, $message, $errorData, $headers);
    }
}
