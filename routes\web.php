<?php

use App\Core\Router;

// Debug Bar assets (only in development) - catch all paths under Resources
Router::get('vendor/php-debugbar/php-debugbar/src/DebugBar/Resources/{file}', 'DebugBarController@assets');

// Test route to verify routing works
Router::get('debug-bar-test', 'DebugBarController@test');

// Enable / Disable debugbar
Router::get('debugbar/enable', 'DebugBarController@enable');
Router::get('debugbar/disable', 'DebugBarController@disable');

// Debug Bar open handler for AJAX requests
Router::get('debugbar/openhandler', 'DebugBarController@openHandler');
Router::post('debugbar/openhandler', 'DebugBarController@openHandler');

// Debug test routes (only in development)
Router::get('debug-test', 'DebugTestController@index');
Router::get('debug-test/ajax', 'DebugTestController@ajax');

// Public routes
Router::get('/', 'HomeController@index', ['name' => 'index']);
Router::get('login', 'AuthController@login', ['name' => 'login']);
Router::post('login', 'AuthController@login', ['name' => 'login.store']);
Router::get('logout', 'AuthController@logout', ['name' => 'lohout']);

// Branch routes (some public, some require auth)
Router::get('branches/available', 'BranchController@getAvailable');
Router::get('branches/current', 'BranchController@current');
Router::get('branches/select', 'BranchController@select');

// Authenticated routes
Router::group(['middleware' => 'auth'], function () {
    Router::get('home', 'HomeController@home', ['name' => 'home']);

    // Branch switching (requires authentication)
    Router::post('branches/switch', 'BranchController@switch');

    // User management routes
    // Router::get('users', 'UserController@index');
    // Router::get('users/{id}', 'UserController@show');
    // Router::post('users', 'UserController@store');

    // Admin routes for branch management
    Router::get('admin/branches', 'BranchController@manage');
    Router::post('admin/branches', 'BranchController@create');
});

Router::group(['prefix' => 'vendor', 'namespace' => 'Vendor', 'middleware' => 'vendor'], function () {
    Router::get('/', 'HomeController@index', ['name' => 'vendor.index']);

    Router::get('/login', 'AuthController@login', ['name' => 'vendor.login']);
    Router::post('/login', 'AuthController@login');
    Router::get('/logout', 'AuthController@logout');

    Router::group(['middleware' => 'auth:vendor'], function () {
        Router::get('/home', 'HomeController@home', ['name' => 'vendor.home']);
    });

    // Router::group(['prefix' => 'vendor2', 'middleware' => 'vendorAuth'], function () {
    //     Router::get('/home', 'HomeController@home');
    // });

    // Router::get('/home', 'HomeController@home', ['name' => 'vendor.login2', 'middleware' => 'vendorAuth']);
});


// Register routes that require authentication
// Router::middleware('HomeController@home', 'auth');

// Router::get('/', 'HomeController@index');
// Router::get('/home', 'HomeController@home');

// Router::get('/login', 'AuthController@login');
// Router::post('/login', 'AuthController@login');

// Router::get('/register', 'AuthController@register');
// Router::post('/register', 'AuthController@register');

