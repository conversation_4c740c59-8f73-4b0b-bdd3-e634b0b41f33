Number.prototype.numberFormat = function(c, d, t){
var n = this,
	c = isNaN(c = Math.abs(c)) ? 2 : c,
	d = d == undefined ? "." : d,
	t = t == undefined ? "," : t,
	s = n < 0 ? "-" : "",
	i = String(parseInt(n = Math.abs(Number(n) || 0).toFixed(c))),
	j = (j = i.length) > 3 ? j % 3 : 0;
 return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
};

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function pFloat(n, forceZero = true){
	var negative = (String(n).charAt(0) == "-") ? 1 : 0;
	n = String(n).replace(/[^0-9.]/g, "");
	n = parseFloat(n);
	if (isNaN(n) && forceZero){
		return 0;
	}
	return negative ? n * -1 : n;
}

function pInt(n, forceZero = true){
	var negative = (String(n).charAt(0) == "-") ? 1 : 0;
	n = String(n).replace(/[^0-9.]/g, "");
	n = parseInt(n,10);
	if (isNaN(n) && forceZero){
			return 0;
	}
	return negative ? n * -1 : n;
}

function round(num,dec){
	if (dec==undefined) dec=2;
	return +(Math.round(num + "e+"+dec)  + "e-"+dec);
}

function nl2br (str, is_xhtml) {
	var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br ' + '/>' : '<br>'; // Adjust comment to avoid issue on phpjs.org display

	return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
}

function iframe_respond(ret){
	if (iframe_callback) {
		iframe_callback(ret);
	}
	else{
		if (ret.status) {
			$.alert({ message:nl2br(ret.msg) });
		}
		else{
			$.alert({ message:nl2br(ret.msg),type:"danger" });
		}
	}
}

async function check_login(show) {
	return Promise.resolve(true);

	// var result=false;
	// var data={ };
	// data[CALL]='check';

	// await $.getJSON(URL_MODULE, data, function(ret){
	// 	if(ret.status){
	// 		result=true;
	// 	}
	// 	else{
	// 		result=false;
	// 	}
	// });

	// if (!result && show){
	// 	show_login();
	// 	return false;
	// }
	// else{
	// 	return result;
	// }
}

function check_login2(show) {
	return Promise.resolve(true);

	// return new Promise(function(myResolve, myReject) {
	// 	var data={ };
	// 	data[CALL]='check';

	// 	$.getJSON(URL_MODULE, data).then(function(result){
	// 		if (result.status) {
	// 			myResolve(); // when successful
	// 		} else {
	// 			myReject();
	// 			if (show) show_login();
	// 		}
	// 	});
	// });
}

function show_login() {
	if(!loginShown){
		loginShown = true;
		$.unblock();
		var tmpl = ['<div class="modal fade" tabindex="-1" role="dialog">',
								'<div class="modal-body p0">',
								'</div>',
								'</div>'].join('');

		$modal=$(tmpl);

		var data={ };
		data[CALL]='ajax_login_form';
		data['branch_code'] = BRANCH;

		$modal.find('.modal-body').html('').load(URL_MODULE, data,function(){


			$modal.modal({
				backdrop:'static',
				keyboard:false,
				show:true,
				width:360,
			});

			var form=$modal.find('form');
			form.on('submit',function(){
				return false;
			});

			$modal.find('input[type="submit"]').on('click',function(e){
				e.preventDefault();

				var data={ };
				data[CALL]='do_login';
				data['branch']=form.find('#branch').val();
				data['u']=form.find('#user').val();
				data['p']=form.find('#pass').val();

				$.ajaxSetup({ async:false });
				$.ajax({
						url: URL_MODULE,
						type: "POST",
						data: data,
				}).done(function(ret){
					$.ajaxSetup({ async:true });

					if (ret=='OK') {
						$modal.modal('hide');
					}
					else{
						$.alert({ message:ret, type:'danger' });
					}
				});
				return false;
			});

			$modal.on({
				'show.bs.modal':function(){

				},
				'shown.bs.modal':function(){
					if($modal.find('form').length<=0){
						$modal.modal('hide');
					}
					else{
						$modal.find('.header').html("Session Timeout.<br/>Please Login");
					}

					$modal.find('input[type="password"]:eq(0)').focus();
				},
				'hide.bs.modal':function(){

				},
				'hidden.bs.modal':function(){
					$modal=null;
					loginShown = false
				},
			});
		});
	}
}

function calc_discount(amt, discount, discount_type){
	var discount_amount=0;

	if (discount_type == "%" || ("" + discount).indexOf("%") > 0){
		discount_amount = pFloat(amt * pFloat(discount)/100);
	}
	else{
		discount_amount = pFloat(discount);
	}

	return discount_amount;
}

function change_branch(args) {
	//code
}

function UpdateQueryString(key, value, url) {
	if (!url) url = window.location.href;
	var re = new RegExp("([?&])" + key + "=.*?(&|#|$)(.*)", "gi"),
			hash;

	if (re.test(url)) {
			if (typeof value !== 'undefined' && value !== null)
					return url.replace(re, '$1' + key + "=" + value + '$2$3');
			else {
					hash = url.split('#');
					url = hash[0].replace(re, '$1$3').replace(/(&|\?)$/, '');
					if (typeof hash[1] !== 'undefined' && hash[1] !== null)
							url += '#' + hash[1];
					return url;
			}
	}
	else {
			if (typeof value !== 'undefined' && value !== null) {
					var separator = url.indexOf('?') !== -1 ? '&' : '?';
					hash = url.split('#');
					url = hash[0] + separator + key + '=' + value;
					if (typeof hash[1] !== 'undefined' && hash[1] !== null)
							url += '#' + hash[1];
					return url;
			}
			else
					return url;
	}
}

function time(){
	var d = new Date();
	return d.getTime();
}
