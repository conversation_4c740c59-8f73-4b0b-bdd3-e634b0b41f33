<?php

namespace App\Middleware;

use App\Exceptions\UnauthorizedException;

class AuthMiddleware
{
    public function handle(): bool
    {
        if (auth()->check()) {
            return true;
        }

        // Check if this is an API request
        if (isApiRequest()) {
            throw new UnauthorizedException('Authentication required to access this resource.');
        }

        if (auth()->getGuard() == 'vendor') {
            redirect(route('vendor.login'));
        }

        // For web requests, redirect to login
        redirect(route('login'));
    }
}
