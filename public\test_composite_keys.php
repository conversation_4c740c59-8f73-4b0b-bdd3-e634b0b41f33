<?php
/**
 * Test file for Composite Primary Keys
 * 
 * This file demonstrates the composite primary key functionality
 */

require_once '../vendor/autoload.php';
require_once '../src/helper.php';

use App\Models\User;
use App\Models\UserPrivilege;
use App\Models\UserBranch;

// Set content type to HTML for better output formatting
header('Content-Type: text/html; charset=UTF-8');

// Simple styling
echo '<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #333; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    .success { color: green; }
    .error { color: red; }
    .example { background: #e9f7fe; padding: 15px; border-left: 4px solid #2196F3; margin-bottom: 20px; }
    .code { font-family: monospace; background: #f0f0f0; padding: 2px 4px; }
    .composite { background: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0; }
</style>';

echo '<h1>Composite Primary Keys Test</h1>';

// Function to display test results
function displayCompositeTest($title, $code, $result) {
    echo '<div class="example">';
    echo "<h3>{$title}</h3>";
    echo '<pre class="code">' . htmlspecialchars($code) . '</pre>';
    echo '<h4>Result:</h4>';
    
    if ($result === null) {
        echo '<p><em>No result (null)</em></p>';
    } elseif ($result === false) {
        echo '<p><em>Operation failed (false)</em></p>';
    } elseif ($result === true) {
        echo '<p class="success">Operation successful (true)</p>';
    } elseif (is_array($result)) {
        if (empty($result)) {
            echo '<p><em>Empty array</em></p>';
        } else {
            echo '<p>Array with ' . count($result) . ' items:</p>';
            echo '<pre>';
            foreach (array_slice($result, 0, 3) as $index => $item) {
                if ($item instanceof App\Core\ModelInstance) {
                    echo "[$index] ModelInstance: " . json_encode($item->toArray()) . "\n";
                    echo "    Primary Key: " . json_encode($item->getKey()) . "\n";
                } else {
                    echo "[$index] " . print_r($item, true) . "\n";
                }
            }
            if (count($result) > 3) {
                echo "... and " . (count($result) - 3) . " more items\n";
            }
            echo '</pre>';
        }
    } elseif ($result instanceof App\Core\ModelInstance) {
        echo '<h5>ModelInstance Object:</h5>';
        echo '<pre>' . json_encode($result->toArray(), JSON_PRETTY_PRINT) . '</pre>';
        echo '<h5>Primary Key:</h5>';
        echo '<pre>' . json_encode($result->getKey()) . '</pre>';
        echo '<h5>Has Composite Key:</h5>';
        echo '<pre>' . ($result->hasCompositePrimaryKey() ? 'Yes' : 'No') . '</pre>';
    } else {
        echo '<pre>';
        print_r($result);
        echo '</pre>';
    }
    
    echo '</div>';
}

echo '<h2>Composite Primary Key Concepts</h2>';

echo '<div class="composite">';
echo '<h3>What are Composite Primary Keys?</h3>';
echo '<p>A composite primary key is a primary key that consists of multiple columns. Instead of using a single column like <code>id</code>, the primary key is made up of two or more columns that together uniquely identify a record.</p>';
echo '<h4>Examples:</h4>';
echo '<ul>';
echo '<li><strong>UserPrivilege:</strong> <code>[user_id, branch_id, privilege]</code></li>';
echo '<li><strong>UserBranch:</strong> <code>[user_id, branch_id]</code></li>';
echo '<li><strong>OrderItem:</strong> <code>[order_id, product_id]</code></li>';
echo '</ul>';
echo '</div>';

echo '<h2>Model Definitions</h2>';

echo '<div class="example">';
echo '<h3>UserPrivilege Model (3-column composite key)</h3>';
echo '<pre class="code">class UserPrivilege extends Model
{
    protected static $primaryKey = [\'user_id\', \'branch_id\', \'privilege\'];
    protected static $fillable = [\'user_id\', \'branch_id\', \'privilege\'];
    protected static $timestamps = false;
}</pre>';
echo '</div>';

echo '<div class="example">';
echo '<h3>UserBranch Model (2-column composite key)</h3>';
echo '<pre class="code">class UserBranch extends Model
{
    protected static $primaryKey = [\'user_id\', \'branch_id\'];
    protected static $fillable = [\'user_id\', \'branch_id\', \'role\', \'access_level\'];
    protected static $timestamps = true;
}</pre>';
echo '</div>';

echo '<h2>Testing Composite Key Operations</h2>';

// Test 1: Find with composite key
try {
    $code = 'UserPrivilege::find([1, 2, \'READ\']); // [user_id, branch_id, privilege]';
    $privilege = UserPrivilege::find([1, 2, 'READ']);
    displayCompositeTest('Find by Composite Key', $code, $privilege);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 2: Create with composite key
try {
    $code = 'UserPrivilege::create([\'user_id\' => 1, \'branch_id\' => 2, \'privilege\' => \'WRITE\']);';
    $result = UserPrivilege::create(['user_id' => 1, 'branch_id' => 2, 'privilege' => 'WRITE']);
    displayCompositeTest('Create with Composite Key', $code, $result);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 3: Find multiple with composite keys
try {
    $code = 'UserPrivilege::findMany([[1, 2, \'READ\'], [1, 2, \'WRITE\']]);';
    $privileges = UserPrivilege::findMany([[1, 2, 'READ'], [1, 2, 'WRITE']]);
    displayCompositeTest('Find Multiple by Composite Keys', $code, $privileges);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 4: UserBranch operations
try {
    $code = 'UserBranch::findByUserAndBranch(1, 2);';
    $userBranch = UserBranch::findByUserAndBranch(1, 2);
    displayCompositeTest('Find UserBranch by User and Branch', $code, $userBranch);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 5: Create UserBranch
try {
    $code = 'UserBranch::create([\'user_id\' => 1, \'branch_id\' => 3, \'role\' => \'manager\']);';
    $result = UserBranch::create(['user_id' => 1, 'branch_id' => 3, 'role' => 'manager']);
    displayCompositeTest('Create UserBranch', $code, $result);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 6: Update with composite key
try {
    $userBranch = UserBranch::find([1, 3]);
    if ($userBranch) {
        $code = '$userBranch->role = \'admin\'; $userBranch->save();';
        $userBranch->role = 'admin';
        $result = $userBranch->save();
        displayCompositeTest('Update with Composite Key', $code, $result);
    }
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 7: Delete with composite key
try {
    $code = 'UserBranch::destroy([1, 3]);';
    $result = UserBranch::destroy([1, 3]);
    displayCompositeTest('Delete with Composite Key', $code, $result);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

echo '<h2>Usage Examples</h2>';

echo '<div class="example">';
echo '<h3>Basic Operations</h3>';
echo '<pre class="code">// Find by composite key
$privilege = UserPrivilege::find([1, 2, \'READ\']);

// Create with composite key
$privilege = UserPrivilege::create([
    \'user_id\' => 1,
    \'branch_id\' => 2,
    \'privilege\' => \'WRITE\'
]);

// Update
$privilege->privilege = \'ADMIN\';
$privilege->save();

// Delete
$privilege->delete();
// or
UserPrivilege::destroy([1, 2, \'WRITE\']);</pre>';
echo '</div>';

echo '<div class="example">';
echo '<h3>Working with Composite Key Values</h3>';
echo '<pre class="code">$privilege = UserPrivilege::find([1, 2, \'READ\']);

// Get composite key as array
$key = $privilege->getKey(); // [1, 2, \'READ\']

// Check if it has composite key
if ($privilege->hasCompositePrimaryKey()) {
    echo "This model uses composite primary key";
}

// Get primary key columns
$columns = $privilege->getPrimaryKeyColumns(); // [\'user_id\', \'branch_id\', \'privilege\']</pre>';
echo '</div>';

echo '<div class="example">';
echo '<h3>Custom Methods for Composite Key Models</h3>';
echo '<pre class="code">// UserBranch specific methods
$userBranch = UserBranch::findByUserAndBranch(1, 2);
$userBranches = UserBranch::forUser(1);
$branchUsers = UserBranch::forBranch(2);

// Create or update
$userBranch = UserBranch::createOrUpdate(1, 2, [
    \'role\' => \'manager\',
    \'access_level\' => \'full\'
]);

// Remove relationship
UserBranch::removeRelationship(1, 2);</pre>';
echo '</div>';

echo '<h2>Database Schema</h2>';

echo '<div class="example">';
echo '<h3>Required Table Structure</h3>';
echo '<pre class="code">-- UserPrivilege table with 3-column composite key
CREATE TABLE user_privilege (
    user_id INT NOT NULL,
    branch_id INT NOT NULL,
    privilege VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, branch_id, privilege),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);

-- UserBranch table with 2-column composite key
CREATE TABLE user_branch (
    user_id INT NOT NULL,
    branch_id INT NOT NULL,
    role VARCHAR(50),
    access_level VARCHAR(50),
    assigned_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, branch_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);</pre>';
echo '</div>';

echo '<h2>Benefits of Composite Primary Keys</h2>';
echo '<ul>';
echo '<li><strong>Natural Relationships</strong> - Represents real-world relationships naturally</li>';
echo '<li><strong>Data Integrity</strong> - Prevents duplicate relationships automatically</li>';
echo '<li><strong>Performance</strong> - Efficient queries on related data</li>';
echo '<li><strong>Storage Efficiency</strong> - No need for artificial ID columns</li>';
echo '<li><strong>Business Logic</strong> - Enforces business rules at database level</li>';
echo '</ul>';

echo '<h2>When to Use Composite Keys</h2>';
echo '<ul>';
echo '<li><strong>Pivot Tables</strong> - Many-to-many relationship tables</li>';
echo '<li><strong>Junction Tables</strong> - Tables that connect other entities</li>';
echo '<li><strong>Time-based Data</strong> - Records with date/time as part of key</li>';
echo '<li><strong>Hierarchical Data</strong> - Parent-child relationships</li>';
echo '<li><strong>Multi-tenant Systems</strong> - Data partitioned by tenant</li>';
echo '</ul>';

echo '<p class="success">Composite primary key testing completed!</p>';

echo '<h2>Next Steps</h2>';
echo '<ul>';
echo '<li>Define composite keys using arrays: <code>protected static $primaryKey = [\'col1\', \'col2\'];</code></li>';
echo '<li>Use arrays when calling find/destroy: <code>Model::find([val1, val2])</code></li>';
echo '<li>Create custom methods for common composite key operations</li>';
echo '<li>Ensure proper database indexes on composite key columns</li>';
echo '</ul>';
?>
