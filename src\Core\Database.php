<?php

namespace App\Core;

use PDO;
use Closure;
use App\Services\DebugBarService;

/**
 * Database Query Builder
 *
 * A fluent query builder for MySQL databases with support for complex queries,
 * joins, subqueries, transactions, and connection management.
 *
 * @package App\Core
 */
class Database
{
    /** @var array Static array to store PDO connections */
    protected static $pdo = [];

    protected static $loggerEnabled = false;

    /** @var string Current database connection name */
    protected $connection = 'default';

    /** @var string Current table name */
    protected $table;

    /** @var array Columns to select */
    protected $selects = ['*'];

    /** @var array JOIN clauses */
    protected $joins = [];

    /** @var array WHERE conditions */
    protected $wheres = [];

    /** @var array GROUP BY columns */
    protected $groupBys = [];

    /** @var array ORDER BY clauses */
    protected $orderBys = [];

    /** @var array HAVING conditions */
    protected $havings = [];

    /** @var int|null LIMIT value */
    protected $limit = null;

    /** @var int|null OFFSET value */
    protected $offset = null;

    /** @var array Parameter bindings for prepared statements */
    protected $bindings = [];

    /**
     * Database constructor
     *
     * @param string $connection Database connection name (default: 'default')
     */
    function __construct($connection = 'default')
    {
        $this->connection = $connection;
    }

    /**
     * Set the database connection
     *
     * @param string $connection Database connection name
     * @return $this
     */
    public function connection($connection)
    {
        $this->connection = $connection;
        return $this;
    }

    /**
     * Set the table for the query
     *
     * @param string $table Table name
     * @return $this
     */
    public function table($table)
    {
        $this->table = $table;
        return $this;
    }

    /**
     * Establish database connection
     *
     * Creates and caches PDO connection instances for reuse.
     * Configures connection with exception mode and associative fetch mode.
     *
     * @return PDO Database connection instance
     * @throws \Exception If database configuration is missing or connection fails
     */
    protected function connect()
    {
        if (!isset(self::$pdo[$this->connection])) {
            $config = isset(config('databases')[$this->connection]) ? config('databases')[$this->connection] : [];

            if (empty($config)) {
                throw new \Exception("Database configuration is not set.");
            }

            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['name']};charset={$config['charset']}";
            self::$pdo[$this->connection] = new PDO($dsn, $config['user'], $config['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);

            self::$pdo[$this->connection]->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

            // Add PDO collector to debug bar if enabled and replace with traceable PDO
            if (DebugBarService::isEnabled()) {
                $traceablePdo = DebugBarService::addPDOCollector(self::$pdo[$this->connection], $this->connection);
                if ($traceablePdo) {
                    self::$pdo[$this->connection] = $traceablePdo;
                }
                $this->logger("Database connection established: {$this->connection}", 'info');
            }
        }

        return self::$pdo[$this->connection];
    }

    protected function logger($message, $level = 'info')
    {
        if (self::$loggerEnabled)
            logger($message, $level);
    }

    /**
     * Parse table name with optional aliasing
     *
     * Handles table name parsing and adds backticks for MySQL compatibility.
     * Supports table aliasing (e.g., "users as u" or "users u").
     *
     * @param string $table Table name with optional alias
     * @return string Formatted table name with backticks
     */
    protected function parseTable($table)
    {
        // Clean and allow aliasing
        if (preg_match('/^(\w+)\s+(as\s+)?(\w+)$/i', $table, $matches)) {
            return "`{$matches[1]}` AS `{$matches[3]}`";
        }

        return "`$table`";
    }

    /**
     * Parse column names with aliasing and table prefixes
     *
     * Handles column parsing with support for:
     * - Wildcards (*, table.*)
     * - Table prefixes (table.column)
     * - Column aliasing (column as alias)
     *
     * @param array $columnsWithAliases Array of column specifications
     * @return string Formatted column list for SQL
     */
    protected function parseColumn(array $columnsWithAliases)
    {
        $columns = [];
        foreach ($columnsWithAliases as $column) {
            if ($column == "*") {
                $columns[] = $column;
            } elseif (preg_match('/^(COUNT|SUM|AVG|MIN|MAX|DISTINCT)\s*\(/i', $column)) {
                // Handle SQL functions - don't wrap in backticks
                $columns[] = $column;
            } elseif(strpos($column, ".*") ) {
                $columns[] = "`" . str_replace(".", "`.", $column);
            } elseif(strpos($column, ".") ) {
                $columns[] = "`" . str_replace(".", "`.`", $column) . "`";
            } elseif (preg_match('/^(\w+)\s+(as\s+)?(\w+)$/i', $column, $matches)) {
                $columns[] = "`{$matches[1]}` AS `{$matches[3]}`";
            } else {
                $columns[] = "`$column`";
            }
        }

        return implode(', ', $columns);
    }

    /**
     * Execute raw SQL query
     *
     * Executes a raw SQL query with optional parameter bindings.
     * Returns different results based on query type:
     * - SELECT queries: Returns array of results
     * - Other queries: Returns boolean indicating success
     *
     * @param string $sql Raw SQL query
     * @param array $bindings Parameter bindings for prepared statement
     * @return array|bool Query results or success status
     * @throws \Exception If database operation fails
     */
    public function raw($sql, array $bindings = [])
    {
        try {
            // Log query in debug bar
            $this->logger("Raw SQL: {$sql}", 'info');
            if (!empty($bindings)) {
                $this->logger("Bindings: " . json_encode($bindings), 'info');
            }

            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($bindings);

            if (str_starts_with(strtolower(trim($sql)), 'select')) {
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }

            return $stmt->rowCount() > 0;
        } catch (\PDOException $e) {
            // Log the error in debug bar
            $this->logger("Database error: " . $e->getMessage(), 'error');

            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    /**
     * Begin database transaction
     *
     * Starts a new database transaction for atomic operations.
     *
     * @throws \Exception If transaction cannot be started
     */
    public function beginTransaction()
    {
        $this->connect()->beginTransaction();
    }

    /**
     * Commit database transaction
     *
     * Commits the current transaction, making all changes permanent.
     *
     * @throws \Exception If commit fails
     */
    public function commit()
    {
        $this->connect()->commit();
    }

    /**
     * Rollback database transaction
     *
     * Rolls back the current transaction, undoing all changes.
     *
     * @throws \Exception If rollback fails
     */
    public function rollback()
    {
        $this->connect()->rollBack();
    }

    /**
     * Set columns to select
     *
     * Replaces the current column selection with the specified columns.
     *
     * @param string ...$columns Column names to select
     * @return $this
     */
    public function select(...$columns)
    {
        $this->selects = $columns;
        return $this;
    }

    /**
     * Add columns to existing selection
     *
     * Adds additional columns to the current selection without replacing existing ones.
     *
     * @param string ...$columns Column names to add to selection
     * @return $this
     */
    public function addSelect(...$columns)
    {
        $this->selects = array_merge($this->selects, $columns);
        return $this;
    }

    /**
     * Add INNER JOIN clause
     *
     * Adds an INNER JOIN to the query with the specified join condition.
     *
     * @param string $table Table to join
     * @param string $first First column in join condition
     * @param string $operator Comparison operator (=, !=, <, >, etc.)
     * @param string $second Second column in join condition
     * @return $this
     */
    public function join($table, $first, $operator, $second)
    {
        $parsed = $this->parseTable($table);
        $this->joins[] = "INNER JOIN {$parsed} ON {$first} {$operator} {$second}";
        return $this;
    }

    /**
     * Add LEFT JOIN clause
     *
     * Adds a LEFT JOIN to the query with the specified join condition.
     *
     * @param string $table Table to join
     * @param string $first First column in join condition
     * @param string $operator Comparison operator (=, !=, <, >, etc.)
     * @param string $second Second column in join condition
     * @return $this
     */
    public function leftJoin($table, $first, $operator, $second)
    {
        $parsed = $this->parseTable($table);
        $this->joins[] = "LEFT JOIN {$parsed} ON {$first} {$operator} {$second}";
        return $this;
    }

    /**
     * Add WHERE condition to query
     *
     * Supports multiple formats:
     * - where('column', 'value') - defaults to = operator
     * - where('column', '>', 'value') - with custom operator
     * - where(function($q) { ... }) - nested conditions
     * - where('column', '=', function($q) { ... }) - subquery conditions
     *
     * @param string|Closure $column Column name or closure for nested conditions
     * @param string|null $operator Comparison operator (=, >, <, !=, etc.)
     * @param mixed $value Value to compare against or closure for subquery
     * @return $this
     */
    public function where($column, $operator = null, $value = null)
    {
        if ($column instanceof Closure) {
            $nested = new self();
            $column($nested);
            $sql = '(' . implode(' ', $nested->wheres) . ')';
            $this->wheres[] = (empty($this->wheres) ? " " : "AND ") . $sql;
            $this->bindings = array_merge($this->bindings, $nested->bindings);
        } else {
            if ($value instanceof Closure) {
                $sub = (new self())->connection($this->connection); // inherit connection
                $value($sub);
                $subQuery = '(' . $sub->toSql() . ')';
                $this->wheres[] = (empty($this->wheres) ? "" : "AND ") . "{$column} {$operator} {$subQuery}";
                $this->bindings = array_merge($this->bindings, $sub->bindings);
            } else {
                if ($value === null) {
                    $value = $operator;
                    $operator = "=";
                }

                $paramKey = ':param_' . count($this->bindings);
                $this->wheres[] = (empty($this->wheres) ? "" : "AND ") . "{$column} {$operator} {$paramKey}";
                $this->bindings[$paramKey] = $value;
            }
        }
        return $this;
    }

    /**
     * Add OR WHERE condition to query
     *
     * Similar to where() but uses OR logic instead of AND.
     * Supports the same formats as where() method.
     *
     * @param string|Closure $column Column name or closure for nested conditions
     * @param string|null $operator Comparison operator (=, >, <, !=, etc.)
     * @param mixed $value Value to compare against or closure for subquery
     * @return $this
     */
    public function orWhere($column, $operator = null, $value = null)
    {
        if ($column instanceof Closure) {
            $nested = new self();
            $column($nested);
            $sql = '(' . implode(' ', $nested->wheres) . ')';
            $this->wheres[] = (empty($this->wheres) ? " " : "OR ") . $sql;
            $this->bindings = array_merge($this->bindings, $nested->bindings);
        } else {
            if ($value instanceof Closure) {
                $sub = (new self())->connection($this->connection); // inherit connection
                $value($sub);
                $subQuery = '(' . $sub->toSql() . ')';
                $this->wheres[] = (empty($this->wheres) ? "" : "OR ") . "{$column} {$operator} {$subQuery}";
                $this->bindings = array_merge($this->bindings, $sub->bindings);
            } else {
                if ($value === null) {
                    $value = $operator;
                    $operator = "=";
                }

                $paramKey = ':param_' . count($this->bindings);
                $this->wheres[] = (empty($this->wheres) ? "" : "OR ") . "{$column} {$operator} {$paramKey}";
                $this->bindings[$paramKey] = $value;
            }
        }
        return $this;
    }

    /**
     * Add WHERE IN condition
     *
     * Checks if column value exists in the provided array of values.
     * Returns early if values array is empty.
     *
     * @param string $column Column name to check
     * @param array $values Array of values to check against
     * @return $this
     */
    public function whereIn($column, array $values)
    {
        if (empty($values)) {
            return $this;
        }

        $paramKeys = [];
        foreach ($values as $value) {
            $paramKey = ':whereIn_' . count($this->bindings);
            $paramKeys[] = $paramKey;
            $this->bindings[$paramKey] = $value;
        }

        $sql = implode(', ', $paramKeys);
        $this->wheres[] = "{$column} IN ({$sql})";
        return $this;
    }

    /**
     * Add WHERE NULL condition
     *
     * Checks if the specified column value is NULL.
     *
     * @param string $column Column name to check for NULL
     * @return $this
     */
    public function whereNull($column)
    {
        $this->wheres[] = "{$column} IS NULL";
        return $this;
    }

    /**
     * Add WHERE BETWEEN condition
     *
     * Checks if column value is between two values (inclusive).
     *
     * @param string $column Column name to check
     * @param array $values Array with exactly two values [min, max]
     * @return $this
     * @throws \InvalidArgumentException If values array doesn't contain exactly 2 elements
     */
    public function whereBetween($column, array $values)
    {
        if (count($values) !== 2) {
            throw new \InvalidArgumentException("whereBetween requires exactly two values.");
        }

        $paramKeys = [];
        foreach ($values as $value) {
            $paramKey = ':whereBetween_' . count($this->bindings);
            $paramKeys[] = $paramKey;
            $this->bindings[$paramKey] = $value;
        }

        $sql = implode(', ', $paramKeys);
        $this->wheres[] = "{$column} BETWEEN {$sql}";
        return $this;
    }

    public function getStatement(): \PDOStatement
    {
        try {
            $sql = $this->toSql();

            // Log query in debug bar            
            $this->logger("Query: {$sql}", 'info');
            if (!empty($this->bindings)) {
                $this->logger("Bindings: " . json_encode($this->bindings), 'info');
            }

            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($this->bindings);

            return $stmt;
        } catch (\PDOException $e) {
            // Log the error in debug bar
            $this->logger("Database error: " . $e->getMessage(), 'error');
            
            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    public function fetch(int $fetchStyle = PDO::FETCH_ASSOC)
    {
        return $this->getStatement()->fetch($fetchStyle);
    }

    public function fetchAllRows(int $fetchStyle = PDO::FETCH_ASSOC): array
    {
        return $this->getStatement()->fetchAll($fetchStyle);
    }

    public function get(): array
    {
        return $this->fetchAllRows();        
    }

    public function first()
    {
        return $this->fetch() ?? false;
    }

    public function fetchColumn(string $column, bool $single = false)
    {
        $this->selects = [$column];
        $sql = $this->toSql();

        $stmt = $this->connect()->prepare($sql);
        $stmt->execute($this->bindings);

        if ($single || $this->limit === 1) {
            return $stmt->fetchColumn();
        }

        $results = [];
        while (($value = $stmt->fetchColumn()) !== false) {
            $results[] = $value;
        }

        return $results;
    }

    public function pluck(string $key, ?string $value = null): array
    {
        $columns = $value ? "{$key}, {$value}" : $key;
        $this->selects = [$columns];

        $sql = $this->toSql();

        $stmt = $this->connect()->prepare($sql);
        $stmt->execute($this->bindings);

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if ($value) {
            $output = [];
            foreach ($results as $row) {
                $output[$row[$key]] = $row[$value];
            }
            return $output;
        }

        return array_column($results, $key);
    }

    public function exists(): bool
    {
        $this->selects = [ 1 ];
        $this->limit = 1;
        $sql = $this->toSql();        

        $stmt = $this->connect()->prepare($sql);
        $stmt->execute($this->bindings);

        return (bool) $stmt->fetchColumn();
    }

    public function count(string $column = '*'): int
    {
        try {
            $this->selects = [ "COUNT($column)" ];

            $sql = $this->toSql();

            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($this->bindings);

            return (int) $stmt->fetchColumn();
        } catch (\PDOException $e) {
            // Log the error or handle it appropriately
            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    public function insert(array $data): bool
    {
        try {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(',:', array_keys($data));

            $parsed = $this->parseTable($this->table);
            $sql = "INSERT INTO {$parsed} ($columns) VALUES ($placeholders)";

            // Log query in debug bar
            $this->logger("Insert Query: {$sql}", 'info');
            $this->logger("Insert Data: " . json_encode($data), 'info');

            $stmt = $this->connect()->prepare($sql);

            return $stmt->execute($data);
        } catch (\PDOException $e) {
            // Log the error in debug bar
            $this->logger("Insert error: " . $e->getMessage(), 'error');
            
            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    /**
     * Insert multiple rows of data
     *
     * Efficiently inserts multiple rows using a single SQL statement.
     * All rows must have the same column structure.
     *
     * @param array $data Array of associative arrays, each representing a row
     * @return bool True if all rows were inserted successfully
     * @throws \Exception If data is empty, rows have different structures, or database operation fails
     */
    public function insertMany(array $data): bool
    {
        if (empty($data)) {
            throw new \Exception("Data array cannot be empty for insertMany()");
        }

        // Ensure all rows have the same structure
        $firstRowKeys = array_keys($data[0]);
        foreach ($data as $index => $row) {
            if (array_keys($row) !== $firstRowKeys) {
                throw new \Exception("All rows must have the same column structure. Row {$index} has different columns.");
            }
        }

        try {
            $columns = implode(',', $firstRowKeys);
            $placeholderRow = '(' . implode(',', array_fill(0, count($firstRowKeys), '?')) . ')';
            $placeholders = implode(',', array_fill(0, count($data), $placeholderRow));

            $parsed = $this->parseTable($this->table);
            $sql = "INSERT INTO {$parsed} ($columns) VALUES $placeholders";

            // Flatten the data array for binding
            $flatData = [];
            foreach ($data as $row) {
                foreach ($firstRowKeys as $key) {
                    $flatData[] = $row[$key];
                }
            }

            // Log query in debug bar            
            $this->logger("Insert Many Query: {$sql}", 'info');
            $this->logger("Insert Many Rows: " . count($data) . " rows", 'info');
            $this->logger("Insert Many Data: " . json_encode($data), 'info');

            $stmt = $this->connect()->prepare($sql);
            return $stmt->execute($flatData);
        } catch (\PDOException $e) {
            // Log the error in debug bar            
            $this->logger("Insert Many error: " . $e->getMessage(), 'error');

            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    /**
     * Replace a single row (MySQL REPLACE INTO)
     *
     * Uses MySQL's REPLACE INTO statement which either inserts a new row
     * or replaces an existing row if a duplicate key is found.
     *
     * @param array $data Associative array of column => value pairs
     * @return bool True if the operation was successful
     * @throws \Exception If data is empty or database operation fails
     */
    public function replace(array $data): bool
    {
        if (empty($data)) {
            throw new \Exception("Data array cannot be empty for replace()");
        }

        try {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(',:', array_keys($data));

            $parsed = $this->parseTable($this->table);
            $sql = "REPLACE INTO {$parsed} ($columns) VALUES ($placeholders)";

            // Log query in debug bar            
            $this->logger("Replace Query: {$sql}", 'info');
            $this->logger("Replace Data: " . json_encode($data), 'info');

            $stmt = $this->connect()->prepare($sql);
            return $stmt->execute($data);
        } catch (\PDOException $e) {
            // Log the error in debug bar
            $this->logger("Replace error: " . $e->getMessage(), 'error');

            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    /**
     * Replace multiple rows (MySQL REPLACE INTO)
     *
     * Uses MySQL's REPLACE INTO statement to insert or replace multiple rows.
     * All rows must have the same column structure.
     *
     * @param array $data Array of associative arrays, each representing a row
     * @return bool True if all rows were processed successfully
     * @throws \Exception If data is empty, rows have different structures, or database operation fails
     */
    public function replaceMany(array $data): bool
    {
        if (empty($data)) {
            throw new \Exception("Data array cannot be empty for replaceMany()");
        }

        // Ensure all rows have the same structure
        $firstRowKeys = array_keys($data[0]);
        foreach ($data as $index => $row) {
            if (array_keys($row) !== $firstRowKeys) {
                throw new \Exception("All rows must have the same column structure. Row {$index} has different columns.");
            }
        }

        try {
            $columns = implode(',', $firstRowKeys);
            $placeholderRow = '(' . implode(',', array_fill(0, count($firstRowKeys), '?')) . ')';
            $placeholders = implode(',', array_fill(0, count($data), $placeholderRow));

            $parsed = $this->parseTable($this->table);
            $sql = "REPLACE INTO {$parsed} ($columns) VALUES $placeholders";

            // Flatten the data array for binding
            $flatData = [];
            foreach ($data as $row) {
                foreach ($firstRowKeys as $key) {
                    $flatData[] = $row[$key];
                }
            }

            // Log query in debug bar            
            $this->logger("Replace Many Query: {$sql}", 'info');
            $this->logger("Replace Many Rows: " . count($data) . " rows", 'info');
            $this->logger("Replace Many Data: " . json_encode($data), 'info');
            

            $stmt = $this->connect()->prepare($sql);
            return $stmt->execute($flatData);
        } catch (\PDOException $e) {
            // Log the error in debug bar
            $this->logger("Replace Many error: " . $e->getMessage(), 'error');

            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    public function lastInsertId(): int
    {
        return $this->connect()->lastInsertId();
    }

    /**
     * Insert multiple rows with transaction support
     *
     * Inserts multiple rows within a transaction for better performance and data integrity.
     * If any row fails, all changes are rolled back.
     *
     * @param array $data Array of associative arrays, each representing a row
     * @param int $batchSize Number of rows to insert per batch (default: 1000)
     * @return bool True if all rows were inserted successfully
     * @throws \Exception If data is empty, rows have different structures, or database operation fails
     */
    public function insertManyWithTransaction(array $data, int $batchSize = 1000): bool
    {
        if (empty($data)) {
            throw new \Exception("Data array cannot be empty for insertManyWithTransaction()");
        }

        // Ensure all rows have the same structure
        $firstRowKeys = array_keys($data[0]);
        foreach ($data as $index => $row) {
            if (array_keys($row) !== $firstRowKeys) {
                throw new \Exception("All rows must have the same column structure. Row {$index} has different columns.");
            }
        }

        try {
            $this->beginTransaction();

            $totalRows = count($data);
            $batches = array_chunk($data, $batchSize);
            $insertedRows = 0;

            // Log transaction start in debug bar
            $this->logger("Starting batch insert transaction: {$totalRows} rows in " . count($batches) . " batches", 'info');

            foreach ($batches as $batchIndex => $batch) {
                $columns = implode(',', $firstRowKeys);
                $placeholderRow = '(' . implode(',', array_fill(0, count($firstRowKeys), '?')) . ')';
                $placeholders = implode(',', array_fill(0, count($batch), $placeholderRow));

                $parsed = $this->parseTable($this->table);
                $sql = "INSERT INTO {$parsed} ($columns) VALUES $placeholders";

                // Flatten the batch data for binding
                $flatData = [];
                foreach ($batch as $row) {
                    foreach ($firstRowKeys as $key) {
                        $flatData[] = $row[$key];
                    }
                }

                // Log batch in debug bar
                $this->logger("Batch " . ($batchIndex + 1) . ": inserting " . count($batch) . " rows", 'info');

                $stmt = $this->connect()->prepare($sql);
                $result = $stmt->execute($flatData);

                if (!$result) {
                    throw new \Exception("Failed to insert batch " . ($batchIndex + 1));
                }

                $insertedRows += count($batch);
            }

            $this->commit();

            // Log success in debug bar
            $this->logger("Batch insert completed successfully: {$insertedRows} rows inserted", 'info');

            return true;
        } catch (\Exception $e) {
            $this->rollback();

            // Log the error in debug bar
            $this->logger("Batch insert failed, transaction rolled back: " . $e->getMessage(), 'error');

            throw new \Exception("Batch insert failed: " . $e->getMessage());
        }
    }

    /**
     * Replace multiple rows with transaction support
     *
     * Replaces multiple rows within a transaction for better performance and data integrity.
     * Uses MySQL's REPLACE INTO with batching for large datasets.
     * If any row fails, all changes are rolled back.
     *
     * @param array $data Array of associative arrays, each representing a row
     * @param int $batchSize Number of rows to replace per batch (default: 1000)
     * @return bool True if all rows were replaced successfully
     * @throws \Exception If data is empty, rows have different structures, or database operation fails
     */
    public function replaceManyWithTransaction(array $data, int $batchSize = 1000): bool
    {
        if (empty($data)) {
            throw new \Exception("Data array cannot be empty for replaceManyWithTransaction()");
        }

        // Ensure all rows have the same structure
        $firstRowKeys = array_keys($data[0]);
        foreach ($data as $index => $row) {
            if (array_keys($row) !== $firstRowKeys) {
                throw new \Exception("All rows must have the same column structure. Row {$index} has different columns.");
            }
        }

        try {
            $this->beginTransaction();

            $totalRows = count($data);
            $batches = array_chunk($data, $batchSize);
            $processedRows = 0;

            // Log transaction start in debug bar
            $this->logger("Starting batch replace transaction: {$totalRows} rows in " . count($batches) . " batches", 'info');

            foreach ($batches as $batchIndex => $batch) {
                $columns = implode(',', $firstRowKeys);
                $placeholderRow = '(' . implode(',', array_fill(0, count($firstRowKeys), '?')) . ')';
                $placeholders = implode(',', array_fill(0, count($batch), $placeholderRow));

                $parsed = $this->parseTable($this->table);
                $sql = "REPLACE INTO {$parsed} ($columns) VALUES $placeholders";

                // Flatten the batch data for binding
                $flatData = [];
                foreach ($batch as $row) {
                    foreach ($firstRowKeys as $key) {
                        $flatData[] = $row[$key];
                    }
                }

                // Log batch in debug bar
                $this->logger("Replace Batch " . ($batchIndex + 1) . ": processing " . count($batch) . " rows", 'info');

                $stmt = $this->connect()->prepare($sql);
                $result = $stmt->execute($flatData);

                if (!$result) {
                    throw new \Exception("Failed to replace batch " . ($batchIndex + 1));
                }

                $processedRows += count($batch);
            }

            $this->commit();

            // Log success in debug bar
            $this->logger("Batch replace completed successfully: {$processedRows} rows processed", 'info');

            return true;
        } catch (\Exception $e) {
            $this->rollback();

            // Log the error in debug bar
            $this->logger("Batch replace failed, transaction rolled back: " . $e->getMessage(), 'error');

            throw new \Exception("Batch replace failed: " . $e->getMessage());
        }
    }

    public function update(array $data): bool
    {
        if (empty($this->wheres)) {
            throw new \Exception("WHERE clause is required for update()");
        }

        try {

            $setClauses = [];
            foreach ($data as $key => $val) {
                $param = ':update_' . $key;
                $setClauses[] = "$key = $param";
                $this->bindings[$param] = $val;
            }

            $parsed = $this->parseTable($this->table);
            $sql = "UPDATE {$parsed} SET " . implode(', ', $setClauses) . " WHERE " . implode(' ', $this->wheres);

            $stmt = $this->connect()->prepare($sql);
            return $stmt->execute(array_merge(array_values($data), $this->bindings));
        } catch (\PDOException $e) {
            // Log the error in debug bar
            $this->logger("Update failed: " . $e->getMessage(), 'error');

            // Log the error or handle it appropriately
            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    public function delete(): bool
    {
        if (empty($this->wheres)) {
            throw new \Exception("WHERE clause is required for delete()");
        }

        try {
            $parsed = $this->parseTable($this->table);
            $sql = "DELETE FROM {$parsed} WHERE " . implode(' AND ', $this->wheres);
            $stmt = $this->connect()->prepare($sql);

            return $stmt->execute($this->bindings);
        } catch (\PDOException $e) {
            // Log the error in debug bar
            $this->logger("Delete failed: " . $e->getMessage(), 'error');

            // Log the error or handle it appropriately
            throw new \Exception("Database error: " . $e->getMessage());
        }
    }

    public function groupBy(string ...$columns)
    {
        $this->groupBys = array_merge($this->groupBys, $columns);
        return $this;
    }

    public function having(string $column, string $operator, mixed $value)
    {
        $paramKey = ':having_' . count($this->bindings);
        $this->havings[] = "{$column} {$operator} {$paramKey}";
        $this->bindings[$paramKey] = $value;
        return $this;
    }

    public function orderBy(string $column, string $direction = 'ASC')
    {
        $this->orderBys[] = "$column " . strtoupper($direction);
        return $this;
    }

    public function limit(int $limit)
    {
        $this->limit = $limit;
        return $this;
    }

    public function offset(int $offset)
    {
        $this->offset = $offset;
        return $this;
    }

    public function toSql(): string
    {
        //$columns = implode(', ', $this->selects);

        $columns = $this->parseColumn($this->selects);

        $parsed = $this->parseTable($this->table);
        $sql = "SELECT {$columns} FROM {$parsed}";

        if (!empty($this->joins)) {
            $sql .= ' ' . implode(' ', $this->joins);
        }

        if (!empty($this->wheres)) {
            $sql .= ' WHERE ' . implode(' ', $this->wheres);
        }

        if (!empty($this->groupBys)) {
            $sql .= ' GROUP BY ' . implode(', ', $this->groupBys);
        }

        if (!empty($this->havings)) {
            $sql .= ' HAVING ' . implode(' AND ', $this->havings);
        }

        if (!empty($this->orderBys)) {
            $sql .= ' ORDER BY ' . implode(', ', $this->orderBys);
        }

        if ($this->limit !== null) {
            $sql .= ' LIMIT ' . $this->limit;
        }

        if ($this->offset !== null) {
            $sql .= ' OFFSET ' . $this->offset;
        }

        return $sql;
    }

    public function debugSql(): string
    {
        $sql = $this->toSql();
        $debugSql = $sql;

        foreach ($this->bindings as $param => $value) {
            $escaped = is_numeric($value) ? $value : "'" . addslashes($value) . "'";
            $debugSql = str_replace($param, $escaped, $debugSql);
        }

        return $debugSql;
    }
}
