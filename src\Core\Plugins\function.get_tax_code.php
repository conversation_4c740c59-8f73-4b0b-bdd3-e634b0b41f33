<?php

function smarty_function_get_tax_code(array $params, Smarty_Internal_Template $template)
{
	$selected=intval($params['selected']);
	$modal = new \Modal\TaxCode();

	$where=array();
	$where['type']=$params['type'];
	$where['active']=1;

	$tax_code=$modal->get_all($where);

	if($params['html']){
		foreach($tax_code as $tc){
			echo '<option data-rate="'.$tc['rate'].'" data-code="'.$tc['code'].'" value="'.$tc['id'].'"';

			if($selected==$tc['id']) echo ' selected="selected"';

			echo '>'.$tc['code'].' - '.$tc['description'].'</option>';
		}
	}
	else{
		$template->assign("tax_code",$tax_code);
	}
}

?>
