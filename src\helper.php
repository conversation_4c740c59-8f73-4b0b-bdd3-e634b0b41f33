<?php

/**
 * Global Helper Functions
 *
 * This file contains utility functions that are available throughout the application.
 * Functions include configuration management, routing helpers, and database access.
 */

if (!function_exists('config')) {
    /**
     * Get or set configuration values
     *
     * Retrieves configuration values using dot notation (e.g., 'database.host').
     * Can also be used to set configuration values when $overwrite is true.
     * Automatically loads environment variables and configuration files on first use.
     *
     * @param string $key Configuration key in dot notation (empty for all config)
     * @param mixed $value Value to set (only used when $overwrite is true)
     * @param bool $overwrite Whether to set the configuration value
     * @return mixed Configuration value or null if not found
     */
    // function config($key = '', $value = null, $overwrite = false)
    // {
    //     static $config;

    //     if (!$config) {
    //         // Load environment variables
    //         $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
    //         $dotenv->safeLoad();

    //         // Load configuration file
    //         $config = require __DIR__ . '/../config/config.php';
    //     }

    //     // If setting a value
    //     if ($overwrite) {
    //         $segments = explode('.', $key);
    //         $ref = &$config;

    //         foreach ($segments as $segment) {
    //             if (!isset($ref[$segment]) || !is_array($ref[$segment])) {
    //                 $ref[$segment] = [];
    //             }
    //             $ref = &$ref[$segment];
    //         }

    //         $ref = $value;
    //         return $value;
    //     }

    //     // If just retrieving
    //     if (empty($key)) {
    //         return $config;
    //     }

    //     $segments = explode('.', $key);
    //     $result = $config;

    //     foreach ($segments as $segment) {
    //         if (is_array($result) && array_key_exists($segment, $result)) {
    //             $result = $result[$segment];
    //         } else {
    //             return null;
    //         }
    //     }

    //     return $result;
    // }

    function config($key = '', $default = null)
    {
        $config = \App\Core\Config::getInstance();
        if (!is_array($key)) {
            return $config->get($key, $default);
        } else {
            $config->set($key);
        }
    }
}

if (!function_exists('route')) {
    /**
     * Generate URL for named route
     *
     * Retrieves the URL path for a named route defined in the routing configuration.
     *
     * @param string $name Named route identifier
     * @return string Route URL path
     * @throws \Exception If named route is not found
     */
    function route($name)
    {
        return \App\Core\Router::getRoute($name);
    }
}

/**
 * Get database connection instance
 *
 * Creates and returns a new Database query builder instance for the specified connection.
 * If no connection is specified, attempts to use the default database connection
 * from session data. Falls back to 'default' connection if no connection is set.
 *
 * @param string|null $connection Database connection name from configuration
 * @return \App\Core\Database Database query builder instance
 */
function DB($connection = 'default')
{
    return new \App\Core\Database($connection);
}

if (!function_exists('currentBranch')) {
    /**
     * Get current branch information
     *
     * Returns the current branch data from session.
     *
     * @return array|null Current branch data or null if not set
     */
    function currentBranch($params = null)
    {
        $field = $params['field'] ?? null;

        // Return specific field if requested
        if (isset($_SESSION['current_branch']) && $field) {
            return $_SESSION['current_branch'][$field] ?? '';
        }

        // Get branch directly from session to avoid circular dependency
        return isset($_SESSION['current_branch']) ? $_SESSION['current_branch'] : null;
    }
}

if (!function_exists('isApiRequest')) {
    /**
     * Check if this is an API request
     */
    function isApiRequest(): bool
    {
        // Check Accept header
        $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
        if (strpos($acceptHeader, 'application/json') !== false) {
            return true;
        }

        // Check Content-Type header
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') !== false) {
            return true;
        }

        // Check if URL starts with /api/
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos(trim($requestUri, '/'), 'api/') === 0) {
            return true;
        }

        // Check for AJAX requests
        $requestedWith = $_SERVER['HTTP_X_REQUESTED_WITH'] ?? '';
        if (strtolower($requestedWith) === 'xmlhttprequest') {
            return true;
        }

        return false;
    }
}

// ===== ERROR RESPONSE HELPERS =====

if (!function_exists('jsonError')) {
    /**
     * Return a JSON error response
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code
     * @param array $data Additional error data
     * @param array $headers Additional headers
     */
    function jsonError(string $message, int $statusCode = 500, array $data = [], array $headers = []): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');

        foreach ($headers as $header => $value) {
            header("$header: $value");
        }

        $response = [
            'success' => false,
            'error' => [
                'code' => $statusCode,
                'message' => $message
            ]
        ];

        if (!empty($data)) {
            $response['error']['data'] = $data;
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
}

if (!function_exists('jsonSuccess')) {
    /**
     * Return a JSON success response
     *
     * @param mixed $data Response data
     * @param string $message Success message
     * @param int $statusCode HTTP status code
     */
    function jsonSuccess($data = null, string $message = 'Success', int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');

        $response = [
            'success' => true,
            'message' => $message
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
}

if (!function_exists('abort')) {
    /**
     * Abort the request with an HTTP error
     *
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    function abort(int $statusCode, string $message = '', array $data = []): void
    {
        $exception = new \App\Exceptions\HttpException($statusCode, $message, $data);
        \App\Services\ErrorHandlerService::handle($exception);
        exit;
    }
}

if (!function_exists('abortIf')) {
    /**
     * Abort the request if condition is true
     *
     * @param bool $condition Condition to check
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    function abortIf(bool $condition, int $statusCode, string $message = '', array $data = []): void
    {
        if ($condition) {
            abort($statusCode, $message, $data);
        }
    }
}

if (!function_exists('abortUnless')) {
    /**
     * Abort the request unless condition is true
     *
     * @param bool $condition Condition to check
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    function abortUnless(bool $condition, int $statusCode, string $message = '', array $data = []): void
    {
        if (!$condition) {
            abort($statusCode, $message, $data);
        }
    }
}

if (!function_exists('class_basename')) {
    /**
     * Get the class "basename" of the given object / class.
     *
     * @param string|object $class
     * @return string
     */
    function class_basename($class): string
    {
        $class = is_object($class) ? get_class($class) : $class;
        return basename(str_replace('\\', '/', $class));
    }
}

if (!function_exists('logger')) {
    function logger($message, $level = 'info'): void
    {
        \App\Services\DebugBarService::log($message, $level);
    }
}

// ===== DEBUGGING HELPERS =====

if (!function_exists('dd')) {
    /**
     * Dump and die - Laravel-style debugging helper
     *
     * Dumps the given variables in a readable format and stops execution.
     * Supports multiple variables and provides detailed information including
     * variable types, array structures, object properties, and more.
     *
     * @param mixed ...$vars Variables to dump
     * @return void
     */
    function dd(...$vars): void
    {
        // Set headers for proper display
        if (!headers_sent()) {
            header('Content-Type: text/html; charset=UTF-8');
        }

        echo '<style>
            .dd-container {
                font-family: "Fira Code", "Monaco", "Consolas", monospace;
                background: #1e1e1e;
                color: #d4d4d4;
                padding: 20px;
                margin: 10px 0;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                overflow-x: auto;
                line-height: 1.4;
            }
            .dd-header {
                color: #569cd6;
                font-weight: bold;
                margin-bottom: 10px;
                border-bottom: 1px solid #333;
                padding-bottom: 5px;
            }
            .dd-type { color: #4ec9b0; font-weight: bold; }
            .dd-string { color: #ce9178; }
            .dd-number { color: #b5cea8; }
            .dd-bool { color: #569cd6; font-weight: bold; }
            .dd-null { color: #808080; font-style: italic; }
            .dd-key { color: #9cdcfe; }
            .dd-arrow { color: #808080; }
            .dd-indent { margin-left: 20px; }
            .dd-trace {
                background: #2d2d30;
                padding: 10px;
                margin-top: 10px;
                border-radius: 4px;
                font-size: 12px;
                color: #cccccc;
            }
        </style>';

        // Get caller information
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $caller = $trace[0] ?? [];

        echo '<div class="dd-container">';
        echo '<div class="dd-header">';
        echo '🐛 DD Debug Output';
        if (isset($caller['file'], $caller['line'])) {
            echo ' - ' . basename($caller['file']) . ':' . $caller['line'];
        }
        echo '</div>';

        if (empty($vars)) {
            echo '<div class="dd-null">No variables provided to dd()</div>';
        } else {
            foreach ($vars as $index => $var) {
                if (count($vars) > 1) {
                    echo '<div class="dd-header">Variable #' . ($index + 1) . ':</div>';
                }
                echo '<div>' . formatVariable($var) . '</div>';
                if ($index < count($vars) - 1) {
                    echo '<hr style="border: 1px solid #333; margin: 15px 0;">';
                }
            }
        }

        // Add stack trace
        echo '<div class="dd-trace">';
        echo '<strong>Stack Trace:</strong><br>';
        foreach (array_slice(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), 1, 5) as $i => $trace) {
            $file = isset($trace['file']) ? basename($trace['file']) : 'unknown';
            $line = $trace['line'] ?? 'unknown';
            $function = $trace['function'] ?? 'unknown';
            $class = isset($trace['class']) ? $trace['class'] . $trace['type'] : '';
            echo sprintf("#%d %s%s() called at %s:%s<br>", $i, $class, $function, $file, $line);
        }
        echo '</div>';

        echo '</div>';

        // Log to debug bar if available
        logger('dd() called with ' . count($vars) . ' variable(s)', 'debug');

        exit(1);
    }
}

if (!function_exists('dump')) {
    /**
     * Dump variables without stopping execution
     *
     * Similar to dd() but doesn't stop execution. Useful for debugging
     * without interrupting the application flow.
     *
     * @param mixed ...$vars Variables to dump
     * @return void
     */
    function dump(...$vars): void
    {
        // Set headers for proper display
        if (!headers_sent()) {
            header('Content-Type: text/html; charset=UTF-8');
        }

        // Get caller information
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $caller = $trace[0] ?? [];

        echo '<div class="dd-container">';
        echo '<div class="dd-header">';
        echo '📋 Dump Output';
        if (isset($caller['file'], $caller['line'])) {
            echo ' - ' . basename($caller['file']) . ':' . $caller['line'];
        }
        echo '</div>';

        if (empty($vars)) {
            echo '<div class="dd-null">No variables provided to dump()</div>';
        } else {
            foreach ($vars as $index => $var) {
                if (count($vars) > 1) {
                    echo '<div class="dd-header">Variable #' . ($index + 1) . ':</div>';
                }
                echo '<div>' . formatVariable($var) . '</div>';
                if ($index < count($vars) - 1) {
                    echo '<hr style="border: 1px solid #333; margin: 15px 0;">';
                }
            }
        }
        echo '</div>';

        // Log to debug bar if available
        logger('dump() called with ' . count($vars) . ' variable(s)', 'debug');
    }
}

if (!function_exists('formatVariable')) {
    /**
     * Format a variable for display in dd/dump functions
     *
     * @param mixed $var Variable to format
     * @param int $depth Current recursion depth
     * @param int $maxDepth Maximum recursion depth
     * @return string Formatted variable string
     */
    function formatVariable($var, int $depth = 0, int $maxDepth = 10): string
    {
        $indent = str_repeat('  ', $depth);
        $type = gettype($var);

        // Prevent infinite recursion
        if ($depth > $maxDepth) {
            return '<span class="dd-type">[MAX DEPTH REACHED]</span>';
        }

        switch ($type) {
            case 'NULL':
                return '<span class="dd-null">null</span>';

            case 'boolean':
                return '<span class="dd-bool">' . ($var ? 'true' : 'false') . '</span>';

            case 'integer':
            case 'double':
                return '<span class="dd-number">' . $var . '</span>';

            case 'string':
                $length = strlen($var);
                $preview = htmlspecialchars($var);
                if ($length > 100) {
                    $preview = htmlspecialchars(substr($var, 0, 100)) . '...';
                }
                return '<span class="dd-string">"' . $preview . '"</span> <span class="dd-type">(string:' . $length . ')</span>';

            case 'array':
                $count = count($var);
                if ($count === 0) {
                    return '<span class="dd-type">array(0)</span> []';
                }

                $result = '<span class="dd-type">array(' . $count . ')</span> [<br>';
                foreach ($var as $key => $value) {
                    $formattedKey = is_string($key) ? '"' . htmlspecialchars($key) . '"' : $key;
                    $result .= $indent . '  <span class="dd-key">' . $formattedKey . '</span> <span class="dd-arrow">=></span> ';
                    $result .= formatVariable($value, $depth + 1, $maxDepth) . '<br>';
                }
                $result .= $indent . ']';
                return $result;

            case 'object':
                $className = get_class($var);
                $reflection = new ReflectionClass($var);
                $properties = $reflection->getProperties();

                $result = '<span class="dd-type">object(' . $className . ')</span> {<br>';

                foreach ($properties as $property) {
                    $property->setAccessible(true);
                    $name = $property->getName();
                    $visibility = '';

                    if ($property->isPrivate()) {
                        $visibility = 'private ';
                    } elseif ($property->isProtected()) {
                        $visibility = 'protected ';
                    } else {
                        $visibility = 'public ';
                    }

                    try {
                        $value = $property->getValue($var);
                        $result .= $indent . '  <span class="dd-type">' . $visibility . '</span>';
                        $result .= '<span class="dd-key">$' . $name . '</span> <span class="dd-arrow">=></span> ';
                        $result .= formatVariable($value, $depth + 1, $maxDepth) . '<br>';
                    } catch (Exception $e) {
                        $result .= $indent . '  <span class="dd-type">' . $visibility . '</span>';
                        $result .= '<span class="dd-key">$' . $name . '</span> <span class="dd-arrow">=></span> ';
                        $result .= '<span class="dd-null">[UNINITIALIZED]</span><br>';
                    }
                }

                $result .= $indent . '}';
                return $result;

            case 'resource':
                return '<span class="dd-type">resource(' . get_resource_type($var) . ')</span>';

            default:
                return '<span class="dd-type">' . $type . '</span> ' . htmlspecialchars(print_r($var, true));
        }
    }
}

if (!function_exists('ddd')) {
    /**
     * Dump, die, and debug - Enhanced dd with more debugging info
     *
     * Like dd() but includes additional debugging information such as
     * memory usage, execution time, and request details.
     *
     * @param mixed ...$vars Variables to dump
     * @return void
     */
    function ddd(...$vars): void
    {
        // Set headers for proper display
        if (!headers_sent()) {
            header('Content-Type: text/html; charset=UTF-8');
        }

        // Get caller information
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $caller = $trace[0] ?? [];

        echo '<div class="dd-container">';
        echo '<div class="dd-header">';
        echo '🔍 DDD Enhanced Debug Output';
        if (isset($caller['file'], $caller['line'])) {
            echo ' - ' . basename($caller['file']) . ':' . $caller['line'];
        }
        echo '</div>';

        // Debug information
        echo '<div class="dd-trace">';
        echo '<strong>Debug Information:</strong><br>';
        echo 'Memory Usage: ' . formatBytes(memory_get_usage(true)) . ' / ' . formatBytes(memory_get_peak_usage(true)) . ' (peak)<br>';
        echo 'Execution Time: ' . round((microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000, 2) . 'ms<br>';
        echo 'Request Method: ' . ($_SERVER['REQUEST_METHOD'] ?? 'CLI') . '<br>';
        echo 'Request URI: ' . ($_SERVER['REQUEST_URI'] ?? 'N/A') . '<br>';
        echo 'User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'N/A') . '<br>';
        echo '</div>';

        if (empty($vars)) {
            echo '<div class="dd-null">No variables provided to ddd()</div>';
        } else {
            foreach ($vars as $index => $var) {
                if (count($vars) > 1) {
                    echo '<div class="dd-header">Variable #' . ($index + 1) . ':</div>';
                }
                echo '<div>' . formatVariable($var) . '</div>';
                if ($index < count($vars) - 1) {
                    echo '<hr style="border: 1px solid #333; margin: 15px 0;">';
                }
            }
        }

        // Add stack trace
        echo '<div class="dd-trace">';
        echo '<strong>Stack Trace:</strong><br>';
        foreach (array_slice(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), 1, 10) as $i => $trace) {
            $file = isset($trace['file']) ? basename($trace['file']) : 'unknown';
            $line = $trace['line'] ?? 'unknown';
            $function = $trace['function'] ?? 'unknown';
            $class = isset($trace['class']) ? $trace['class'] . $trace['type'] : '';
            echo sprintf("#%d %s%s() called at %s:%s<br>", $i, $class, $function, $file, $line);
        }
        echo '</div>';

        echo '</div>';

        // Log to debug bar if available
        logger('ddd() called with ' . count($vars) . ' variable(s)', 'debug');

        exit(1);
    }
}

if (!function_exists('ray')) {
    /**
     * Ray-style debugging helper
     *
     * Logs variables to the debug bar and returns the first variable
     * for method chaining. Inspired by Spatie's Ray.
     *
     * @param mixed ...$vars Variables to log
     * @return mixed First variable for chaining
     */
    function ray(...$vars)
    {
        if (empty($vars)) {
            logger('ray() called with no variables', 'debug');
            return null;
        }

        foreach ($vars as $index => $var) {
            $type = gettype($var);
            $preview = '';

            switch ($type) {
                case 'string':
                    $preview = strlen($var) > 50 ? substr($var, 0, 50) . '...' : $var;
                    break;
                case 'array':
                    $preview = 'Array(' . count($var) . ')';
                    break;
                case 'object':
                    $preview = get_class($var);
                    break;
                case 'boolean':
                    $preview = $var ? 'true' : 'false';
                    break;
                case 'NULL':
                    $preview = 'null';
                    break;
                default:
                    $preview = (string) $var;
            }

            logger("Ray #" . ($index + 1) . ": ({$type}) {$preview}", 'debug');
        }

        // Return first variable for chaining
        return $vars[0];
    }
}

if (!function_exists('formatBytes')) {
    /**
     * Format bytes into human readable format
     *
     * @param int $bytes Number of bytes
     * @param int $precision Decimal precision
     * @return string Formatted string
     */
    function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (!function_exists('auth')) {
    function auth(): App\Core\Auth
    {
        return \App\Core\Auth::getAuth();
    }
}

if (!function_exists('redirect')) {
    function redirect($url, $statusCode = 303)
    {
        header('Location: ' . $url, true, $statusCode);
        die();
    }
}