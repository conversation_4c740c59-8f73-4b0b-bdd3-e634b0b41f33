<?php

/**
 * Global Helper Functions
 *
 * This file contains utility functions that are available throughout the application.
 * Functions include configuration management, routing helpers, and database access.
 */

if (!function_exists('config')) {
    /**
     * Get or set configuration values
     *
     * Retrieves configuration values using dot notation (e.g., 'database.host').
     * Can also be used to set configuration values when $overwrite is true.
     * Automatically loads environment variables and configuration files on first use.
     *
     * @param string $key Configuration key in dot notation (empty for all config)
     * @param mixed $value Value to set (only used when $overwrite is true)
     * @param bool $overwrite Whether to set the configuration value
     * @return mixed Configuration value or null if not found
     */
    // function config($key = '', $value = null, $overwrite = false)
    // {
    //     static $config;

    //     if (!$config) {
    //         // Load environment variables
    //         $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');
    //         $dotenv->safeLoad();

    //         // Load configuration file
    //         $config = require __DIR__ . '/../config/config.php';
    //     }

    //     // If setting a value
    //     if ($overwrite) {
    //         $segments = explode('.', $key);
    //         $ref = &$config;

    //         foreach ($segments as $segment) {
    //             if (!isset($ref[$segment]) || !is_array($ref[$segment])) {
    //                 $ref[$segment] = [];
    //             }
    //             $ref = &$ref[$segment];
    //         }

    //         $ref = $value;
    //         return $value;
    //     }

    //     // If just retrieving
    //     if (empty($key)) {
    //         return $config;
    //     }

    //     $segments = explode('.', $key);
    //     $result = $config;

    //     foreach ($segments as $segment) {
    //         if (is_array($result) && array_key_exists($segment, $result)) {
    //             $result = $result[$segment];
    //         } else {
    //             return null;
    //         }
    //     }

    //     return $result;
    // }

    function config($key = '', $default = null)
    {
        $config = \App\Core\Config::getInstance();
        if (!is_array($key)) {
            return $config->get($key, $default);
        } else {
            $config->set($key);
        }
    }
}

if (!function_exists('route')) {
    /**
     * Generate URL for named route
     *
     * Retrieves the URL path for a named route defined in the routing configuration.
     *
     * @param string $name Named route identifier
     * @return string Route URL path
     * @throws \Exception If named route is not found
     */
    function route($name)
    {
        return \App\Core\Router::getRoute($name);
    }
}

/**
 * Get database connection instance
 *
 * Creates and returns a new Database query builder instance for the specified connection.
 * If no connection is specified, attempts to use the default database connection
 * from session data. Falls back to 'default' connection if no connection is set.
 *
 * @param string|null $connection Database connection name from configuration
 * @return \App\Core\Database Database query builder instance
 */
function DB($connection = 'default')
{
    return new \App\Core\Database($connection);
}

if (!function_exists('currentBranch')) {
    /**
     * Get current branch information
     *
     * Returns the current branch data from session.
     *
     * @return array|null Current branch data or null if not set
     */
    function currentBranch($params = null)
    {
        $field = $params['field'] ?? null;

        // Return specific field if requested
        if (isset($_SESSION['current_branch']) && $field) {
            return $_SESSION['current_branch'][$field] ?? '';
        }

        // Get branch directly from session to avoid circular dependency
        return isset($_SESSION['current_branch']) ? $_SESSION['current_branch'] : null;
    }
}

if (!function_exists('isApiRequest')) {
    /**
     * Check if this is an API request
     */
    function isApiRequest(): bool
    {
        // Check Accept header
        $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
        if (strpos($acceptHeader, 'application/json') !== false) {
            return true;
        }

        // Check Content-Type header
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') !== false) {
            return true;
        }

        // Check if URL starts with /api/
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos(trim($requestUri, '/'), 'api/') === 0) {
            return true;
        }

        // Check for AJAX requests
        $requestedWith = $_SERVER['HTTP_X_REQUESTED_WITH'] ?? '';
        if (strtolower($requestedWith) === 'xmlhttprequest') {
            return true;
        }

        return false;
    }
}

// ===== ERROR RESPONSE HELPERS =====

if (!function_exists('jsonError')) {
    /**
     * Return a JSON error response
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code
     * @param array $data Additional error data
     * @param array $headers Additional headers
     */
    function jsonError(string $message, int $statusCode = 500, array $data = [], array $headers = []): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');

        foreach ($headers as $header => $value) {
            header("$header: $value");
        }

        $response = [
            'success' => false,
            'error' => [
                'code' => $statusCode,
                'message' => $message
            ]
        ];

        if (!empty($data)) {
            $response['error']['data'] = $data;
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
}

if (!function_exists('jsonSuccess')) {
    /**
     * Return a JSON success response
     *
     * @param mixed $data Response data
     * @param string $message Success message
     * @param int $statusCode HTTP status code
     */
    function jsonSuccess($data = null, string $message = 'Success', int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');

        $response = [
            'success' => true,
            'message' => $message
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
}

if (!function_exists('abort')) {
    /**
     * Abort the request with an HTTP error
     *
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    function abort(int $statusCode, string $message = '', array $data = []): void
    {
        $exception = new \App\Exceptions\HttpException($statusCode, $message, $data);
        \App\Services\ErrorHandlerService::handle($exception);
        exit;
    }
}

if (!function_exists('abortIf')) {
    /**
     * Abort the request if condition is true
     *
     * @param bool $condition Condition to check
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    function abortIf(bool $condition, int $statusCode, string $message = '', array $data = []): void
    {
        if ($condition) {
            abort($statusCode, $message, $data);
        }
    }
}

if (!function_exists('abortUnless')) {
    /**
     * Abort the request unless condition is true
     *
     * @param bool $condition Condition to check
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $data Additional error data
     */
    function abortUnless(bool $condition, int $statusCode, string $message = '', array $data = []): void
    {
        if (!$condition) {
            abort($statusCode, $message, $data);
        }
    }
}

if (!function_exists('class_basename')) {
    /**
     * Get the class "basename" of the given object / class.
     *
     * @param string|object $class
     * @return string
     */
    function class_basename($class): string
    {
        $class = is_object($class) ? get_class($class) : $class;
        return basename(str_replace('\\', '/', $class));
    }
}

if (!function_exists('logger')) {
    function logger($message, $level = 'info'): void
    {
        \App\Services\DebugBarService::log($message, $level);
    }
}

if (!function_exists('auth')) {
    function auth(): App\Core\Auth
    {
        return \App\Core\Auth::getAuth();
    }
}

if (!function_exists('redirect')) {
    function redirect($url, $statusCode = 303)
    {
        header('Location: ' . $url, true, $statusCode);
        die();
    }
}