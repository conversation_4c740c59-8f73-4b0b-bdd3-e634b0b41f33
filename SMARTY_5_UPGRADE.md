# Smarty 5.5 Upgrade Guide

This document outlines the changes made to support Smarty 5.5 and resolve the "Creation of dynamic property" deprecation warnings.

## Changes Made

### 1. Updated Smarty Initialization

**Controller.php:**
- Changed from `new \Smarty()` to `new \Smarty\Smarty()`
- Added automatic helper function registration in the constructor

**ErrorHandlerService.php:**
- Updated to use `new \Smarty\Smarty()`
- Added helper function registration for error templates

### 2. Function Registration

In Smarty 5.5, all PHP functions must be registered before they can be used in templates. The following functions are now automatically registered:

**Template Functions:**
- `config` - Access configuration values
- `route` - Generate route URLs
- `currentBranch` - Get current branch information

**Template Modifiers:**
- `date_format`, `number_format`, `ucfirst`, `strtolower`, `strtoupper`
- `trim`, `substr`, `strlen`, `htmlspecialchars`, `nl2br`, `strip_tags`
- `urlencode`, `urldecode`, `json_encode`
- `count`, `implode`, `array_keys`, `array_values`

### 3. Template Syntax Changes

**Old Syntax (Smarty 3/4):**
```smarty
{config('app.name', 'Default')}
```

**New Syntax (Smarty 5.5):**
```smarty
{config key='app.name' default='Default'}
```

**Debug Conditions:**
```smarty
{* Old *}
{if $smarty.const.APP_DEBUG && $debug_info}

{* New *}
{if {config key='app.debug' default=false} && $debug_info}
```

### 4. Files Updated

- `src/Controller/Controller.php` - Added `registerHelperFunctions()` method
- `src/Services/ErrorHandlerService.php` - Added `registerErrorTemplateHelpers()` method
- `templates/base.tpl` - Updated config function call syntax
- `templates/errors/404.tpl` - Updated debug condition
- `templates/errors/403.tpl` - Updated debug condition
- `templates/errors/500.tpl` - Updated debug condition

## Adding New Helper Functions

To add new helper functions for use in templates, update the `registerHelperFunctions()` method in `Controller.php`:

```php
// Register a new function
$this->smarty->registerPlugin('function', 'my_function', function($params) {
    $value = $params['value'] ?? '';
    return my_helper_function($value);
});

// Register a new modifier
$this->smarty->registerPlugin('modifier', 'my_modifier', 'my_modifier_function');
```

## Template Usage Examples

```smarty
{* Configuration access *}
<title>{config key='app.name' default='My App'}</title>

{* Route generation *}
<a href="{route name='home'}">Home</a>

{* Current branch *}
{assign var="branch" value={currentBranch}}
{if $branch}
    Current Branch: {$branch.name}
{/if}

{* Using modifiers *}
{$text|trim|ucfirst}
{$date|date_format:'Y-m-d'}
{$array|count}
```

## Troubleshooting

### Function Not Found Errors
If you get "function not found" errors in templates:
1. Check if the function is registered in `registerHelperFunctions()`
2. Ensure the function exists in `helper.php` or is a valid PHP function
3. Use the correct Smarty 5.5 syntax with named parameters

### Deprecation Warnings
If you still see deprecation warnings:
1. Check for any remaining old-style function calls in templates
2. Ensure all templates use the new `{config key='...' default='...'}` syntax
3. Update any custom plugins to use the new Smarty 5.5 API

### Template Compilation Errors
If templates fail to compile:
1. Clear the `templates_c/` directory
2. Check file permissions on template directories
3. Verify template syntax matches Smarty 5.5 requirements

## Performance Notes

- Function registration happens once per request in the Controller constructor
- Compiled templates are cached, so function registration overhead is minimal
- Error templates have their own function registration to avoid dependencies
