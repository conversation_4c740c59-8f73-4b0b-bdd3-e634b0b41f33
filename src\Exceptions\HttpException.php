<?php

namespace App\Exceptions;

use Exception;

/**
 * Base HTTP Exception class
 * 
 * Provides a foundation for HTTP-specific exceptions with status codes,
 * headers, and structured error data.
 */
class HttpException extends Exception
{
    protected int $statusCode;
    protected array $headers;
    protected array $errorData;

    /**
     * Create a new HTTP exception
     *
     * @param int $statusCode HTTP status code
     * @param string $message Error message
     * @param array $errorData Additional error data
     * @param array $headers HTTP headers to send
     * @param Exception|null $previous Previous exception
     */
    public function __construct(
        int $statusCode = 500,
        string $message = 'Internal Server Error',
        array $errorData = [],
        array $headers = [],
        Exception $previous = null
    ) {
        $this->statusCode = $statusCode;
        $this->headers = $headers;
        $this->errorData = $errorData;

        parent::__construct($message, $statusCode, $previous);
    }

    /**
     * Get the HTTP status code
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the HTTP headers
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    /**
     * Get additional error data
     */
    public function getErrorData(): array
    {
        return $this->errorData;
    }

    /**
     * Set additional error data
     */
    public function setErrorData(array $errorData): self
    {
        $this->errorData = $errorData;
        return $this;
    }

    /**
     * Add a single error data item
     */
    public function addErrorData(string $key, $value): self
    {
        $this->errorData[$key] = $value;
        return $this;
    }

    /**
     * Check if this is a client error (4xx)
     */
    public function isClientError(): bool
    {
        return $this->statusCode >= 400 && $this->statusCode < 500;
    }

    /**
     * Check if this is a server error (5xx)
     */
    public function isServerError(): bool
    {
        return $this->statusCode >= 500 && $this->statusCode < 600;
    }

    /**
     * Get a user-friendly error title based on status code
     */
    public function getTitle(): string
    {
        return match ($this->statusCode) {
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            422 => 'Unprocessable Entity',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable',
            504 => 'Gateway Timeout',
            default => 'HTTP Error'
        };
    }

    /**
     * Get a default user-friendly message based on status code
     */
    public function getDefaultMessage(): string
    {
        return match ($this->statusCode) {
            400 => 'The request could not be understood by the server.',
            401 => 'Authentication is required to access this resource.',
            403 => 'You do not have permission to access this resource.',
            404 => 'The requested resource could not be found.',
            405 => 'The request method is not allowed for this resource.',
            422 => 'The request data could not be processed.',
            429 => 'Too many requests. Please try again later.',
            500 => 'An internal server error occurred.',
            502 => 'Bad gateway error occurred.',
            503 => 'The service is temporarily unavailable.',
            504 => 'Gateway timeout occurred.',
            default => 'An HTTP error occurred.'
        };
    }
}
