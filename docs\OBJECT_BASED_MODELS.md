# Object-Based Models

This document explains the enhanced object-based model functionality that returns ModelInstance objects instead of arrays.

## Overview

The models now return `ModelInstance` objects instead of arrays, providing a more object-oriented approach to working with database records. This gives you:

- **Property Access**: `$user->name` instead of `$user['name']`
- **Object Methods**: `$user->save()`, `$user->delete()`, `$user->refresh()`
- **Better IDE Support**: Autocomplete and type hints
- **Fluent Interface**: Method chaining capabilities
- **Laravel-like Experience**: Similar to Eloquent models

## ModelInstance Class

The `ModelInstance` class represents a single database record as an object with dynamic properties and helper methods.

### Property Access

```php
$user = User::find(1);

// Access properties directly
echo $user->name;
echo $user->email;
echo $user->id;

// Set properties
$user->name = "New Name";
$user->email = "<EMAIL>";

// Check if property exists
if (isset($user->profile_image)) {
    echo $user->profile_image;
}
```

### Object Methods

#### save(): bool
Save changes to the database:
```php
$user = User::find(1);
$user->name = "Updated Name";
$user->save(); // Updates the database
```

#### delete(): bool
Delete the record from database:
```php
$user = User::find(1);
$user->delete(); // Removes from database
```

#### refresh(): bool
Refresh the object from database:
```php
$user = User::find(1);
$user->refresh(); // Reloads data from database
```

#### update(array $attributes): bool
Update multiple attributes at once:
```php
$user = User::find(1);
$user->update([
    'name' => 'New Name',
    'email' => '<EMAIL>'
]);
```

#### toArray(): array
Convert to array:
```php
$user = User::find(1);
$userData = $user->toArray();
// Returns: ['id' => 1, 'name' => 'John', 'email' => '<EMAIL>', ...]
```

#### toJson(): string
Convert to JSON:
```php
$user = User::find(1);
$jsonData = $user->toJson();
// Returns: '{"id":1,"name":"John","email":"<EMAIL>",...}'
```

#### exists(): bool
Check if record exists in database:
```php
$user = User::find(1);
if ($user->exists()) {
    echo "User exists in database";
}
```

#### getKey(): mixed
Get the primary key value:
```php
$user = User::find(1);
echo $user->getKey(); // Returns: 1
```

## Static Methods Return Objects

All static methods now return `ModelInstance` objects or collections of objects:

### Single Record Methods

```php
// Returns ModelInstance or null
$user = User::find(1);
$user = User::findOrFail(1);
$user = User::first();
$user = User::latest();
$user = User::whereFirst('username', 'admin');
$user = User::findByUsername('admin');

// Returns ModelInstance or false
$user = User::create(['name' => 'John', 'username' => 'john']);
```

### Collection Methods

```php
// Returns array of ModelInstance objects
$users = User::all();
$users = User::findMany([1, 2, 3]);
$users = User::active();
$users = User::byRole('admin');

// Iterate through collection
foreach ($users as $user) {
    echo $user->name; // Each item is a ModelInstance
    echo $user->email;
}
```

## Usage Examples

### Basic CRUD Operations

```php
// Create
$user = User::create([
    'name' => 'John Doe',
    'username' => 'johndoe',
    'password' => 'secret123',
    'email' => '<EMAIL>'
]);
echo $user->id; // Get the new ID

// Read
$user = User::find(1);
echo $user->name;
echo $user->email;

// Update
$user->name = "John Updated";
$user->email = "<EMAIL>";
$user->save();

// Or use update method
$user->update([
    'name' => 'John Updated Again',
    'email' => '<EMAIL>'
]);

// Delete
$user->delete();
```

### Working with Collections

```php
// Get all active users
$activeUsers = User::active();

foreach ($activeUsers as $user) {
    echo "User: {$user->name} ({$user->email})\n";
    
    // Update each user
    $user->last_seen = date('Y-m-d H:i:s');
    $user->save();
}

// Convert collection to array
$usersArray = array_map(function($user) {
    return $user->toArray();
}, $activeUsers);
```

### Advanced Object Operations

```php
// Find and modify
$user = User::findByUsername('admin');
if ($user) {
    $user->last_login = date('Y-m-d H:i:s');
    $user->login_count = ($user->login_count ?? 0) + 1;
    $user->save();
}

// Conditional updates
$users = User::where('active', 0)->get();
foreach ($users as $user) {
    if (strtotime($user->created_at) < strtotime('-30 days')) {
        $user->delete(); // Delete inactive users older than 30 days
    }
}

// Bulk operations with objects
$newUsers = User::findMany([1, 2, 3, 4, 5]);
foreach ($newUsers as $user) {
    $user->status = 'verified';
    $user->verified_at = date('Y-m-d H:i:s');
    $user->save();
}
```

## Benefits

### Developer Experience
- **Intuitive Syntax**: `$user->name` instead of `$user['name']`
- **IDE Support**: Better autocomplete and type hints
- **Method Chaining**: Fluent interface for operations
- **Object-Oriented**: More natural PHP object usage

### Functionality
- **Instance Methods**: Save, delete, refresh operations
- **Property Access**: Direct property getting/setting
- **Type Safety**: Better error detection
- **Consistency**: Same interface across all models

### Backward Compatibility
- **Array Access**: Objects can still be converted to arrays
- **Existing Code**: No breaking changes to static methods
- **Migration Path**: Gradual adoption possible

## Migration from Arrays

### Before (Array-based)
```php
$user = User::find(1);
echo $user['name'];
echo $user['email'];

// Update
User::updateById(1, ['name' => 'Updated']);
```

### After (Object-based)
```php
$user = User::find(1);
echo $user->name;
echo $user->email;

// Update
$user->name = 'Updated';
$user->save();
```

## Creating Custom Models

When creating new models, they automatically inherit object functionality:

```php
<?php

namespace App\Models;

use App\Core\Model;

class Product extends Model
{
    protected static $table = 'products';
    protected static $fillable = ['name', 'price', 'category_id'];
    
    // Custom methods
    public static function expensive()
    {
        $records = static::where('price', '>', 1000)->get();
        return static::newCollection($records);
    }
}

// Usage
$product = Product::find(1);
echo $product->name;
echo $product->price;

$product->price = 999.99;
$product->save();

$expensiveProducts = Product::expensive();
foreach ($expensiveProducts as $product) {
    echo $product->name;
}
```

## Debugging and Development

### Using dd() with Objects

```php
$user = User::find(1);
dd($user); // Shows beautiful object structure

$users = User::all();
dd($users); // Shows collection of objects
```

### Object Inspection

```php
$user = User::find(1);

// Check object state
var_dump($user->exists()); // bool(true)
var_dump($user->getKey()); // int(1)

// Convert for debugging
print_r($user->toArray());
echo $user->toJson();
```

## Performance Considerations

### Memory Usage
- Objects use slightly more memory than arrays
- Collections of objects are still efficient
- Lazy loading prevents unnecessary object creation

### Database Operations
- Same underlying database queries
- No performance impact on database operations
- Object creation happens after data retrieval

### Best Practices
- Use `toArray()` when passing to functions expecting arrays
- Convert to JSON for API responses
- Use object methods for cleaner code

## Error Handling

```php
try {
    $user = User::findOrFail(999); // Throws exception if not found
    $user->name = 'Updated';
    $user->save();
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

// Safe operations
$user = User::find(999);
if ($user) {
    echo $user->name;
} else {
    echo "User not found";
}
```

## Testing

Test the object functionality using the provided test page:
- Visit `/test_user_model.php` in your browser
- See live examples of object operations
- Test property access and method calls
- Verify object behavior with real data

## Compatibility

- **PHP Version**: 7.4+
- **Existing Code**: Fully backward compatible
- **Array Functions**: Objects can be converted to arrays
- **JSON APIs**: Objects can be converted to JSON
- **Debug Tools**: Works with dd(), var_dump(), print_r()

The object-based approach provides a more modern, intuitive way to work with database records while maintaining full compatibility with existing array-based code.
