<?php

namespace App\Controllers;

use App\Services\DebugBarService;

/**
 * Debug Bar Controller
 * 
 * Handles serving debug bar assets (CSS, JS, fonts) in development mode.
 */
class DebugBarController
{
    /**
     * Serve debug bar assets
     * 
     * @param string $file
     */
    public function assets($file = '')
    {
        // Only serve assets in debug mode
        if (!config('app.debug', false)) {
            http_response_code(404);
            echo "404 Not Found";
            return;
        }
        
        $debugBarPath = __DIR__ . '/../../vendor/php-debugbar/php-debugbar/src/DebugBar/Resources/';
        $filePath = $debugBarPath . $file;
        
        // Security check - ensure file is within debug bar resources directory
        $realDebugBarPath = realpath($debugBarPath);
        $realFilePath = realpath($filePath);
        
        if (!$realFilePath || strpos($realFilePath, $realDebugBarPath) !== 0) {
            http_response_code(404);
            echo "404 Not Found";
            return;
        }
        
        if (!file_exists($filePath)) {
            http_response_code(404);
            echo "404 Not Found";
            return;
        }
        
        // Set appropriate content type
        $extension = pathinfo($file, PATHINFO_EXTENSION);
        $contentTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'svg' => 'image/svg+xml',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject'
        ];
        
        if (isset($contentTypes[$extension])) {
            header('Content-Type: ' . $contentTypes[$extension]);
        }
        
        // Set cache headers for assets
        header('Cache-Control: public, max-age=3600');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
        
        // Output the file
        readfile($filePath);
    }

    /**
     * Test method to verify routing works
     */
    public function test()
    {
        // Add some test data to debug bar
        logger('Debug bar test page loaded', 'info');
        logger('Testing setOpenHandlerUrl configuration', 'debug');
        logger('This is a warning message', 'warning');
        logger('This is an error message', 'error');

        // Simulate some database activity if available
        try {
            $users = \App\Models\User::all();
            logger('Database query executed: all()', 'info');
        } catch (\Exception $e) {
            logger('Database test failed: ' . $e->getMessage(), 'error');
        }

        // Create a simple HTML page with debug bar
        $html = '<!DOCTYPE html>
<html>
<head>
    <title>Debug Bar Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-info { background: #f0f0f0; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .success { color: green; }
        .info { color: blue; }
    </style>';

        // Add debug bar head content
        if (DebugBarService::isEnabled()) {
            $html .= DebugBarService::renderHead();
        }

        $html .= '</head>
<body>
    <h1>PHP Debug Bar Test Page</h1>

    <div class="test-info">
        <h3>Debug Bar Configuration Test</h3>
        <p class="success">✓ Debug bar is enabled and working</p>
        <p class="info">✓ setOpenHandlerUrl is configured to: <code>/debugbar/openhandler</code></p>
        <p class="info">✓ Storage is configured in: <code>logs/debugbar/</code></p>
        <p class="info">✓ Multiple test messages have been logged</p>
    </div>

    <div class="test-info">
        <h3>How to Test</h3>
        <ol>
            <li>Look for the debug bar at the bottom of this page</li>
            <li>Click on different tabs (Messages, Time, Memory, etc.)</li>
            <li>Open browser developer tools and check the Network tab</li>
            <li>Click on debug bar tabs to see AJAX requests to <code>/debugbar/openhandler</code></li>
            <li>The open handler should return detailed JSON data for each request</li>
        </ol>
    </div>

    <div class="test-info">
        <h3>Available Debug Bar Features</h3>
        <ul>
            <li><strong>Messages:</strong> Custom log messages (check for test messages)</li>
            <li><strong>Time:</strong> Performance timing information</li>
            <li><strong>Memory:</strong> Memory usage tracking</li>
            <li><strong>Request:</strong> HTTP request details</li>
            <li><strong>Config:</strong> Application configuration</li>
            <li><strong>Exceptions:</strong> Exception tracking</li>
        </ul>
    </div>';

        // Add debug bar body content
        if (DebugBarService::isEnabled()) {
            $html .= DebugBarService::render();
        }

        $html .= '</body></html>';

        echo $html;
    }

    /**
     * Test method to verify routing works
     */
    public function enable()
    {
        $_SESSION['debugbar_enabled'] = true;

        redirect($_SERVER['HTTP_REFERER'] ?? '/');
    }

    /**
     * Test method to verify routing works
     */
    public function disable()
    {
        unset($_SESSION['debugbar_enabled']);

        redirect($_SERVER['HTTP_REFERER'] ?? '/');
    }

    /**
     * Handle debug bar open handler requests
     *
     * This endpoint handles AJAX requests from the debug bar for detailed information
     */
    public function openHandler()
    {
        // Only allow in debug mode
        if (!DebugBarService::isEnabled()) {
            jsonError('Debug bar not enabled', 404);
        }

        $debugBar = DebugBarService::getDebugBar();
        if (!$debugBar) {
            jsonError('Debug bar not available', 404);
        }

        $openHandler = DebugBarService::getOpenHandler();
        $openHandler->handle(null, true, true);
    }
}
