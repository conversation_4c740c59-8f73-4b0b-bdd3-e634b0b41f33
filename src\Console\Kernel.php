<?php

namespace App\Console;

/**
 * Console Kernel
 *
 * Manages and dispatches console commands for the application.
 * Handles command registration, discovery, and execution.
 */
class Kernel
{
    /** @var array Registered console commands */
    protected $commands = [];

    /**
     * Kernel constructor
     *
     * Initializes the console kernel and loads default commands.
     */
    public function __construct()
    {
        $this->loadDefaultCommands();
    }

    /**
     * Load default console commands
     *
     * Registers the built-in console commands including migration,
     * route management, and testing commands.
     */
    protected function loadDefaultCommands()
    {
        $this->commands = [
            'make:migration'     => \App\Console\Commands\Migration\MakeCommand::class,
            'migrate'            => \App\Console\Commands\Migration\MigrateCommand::class,

            'route:cache'        => \App\Console\Commands\Route\CacheCommand::class,
            'route:clear'        => \App\Console\Commands\Route\ClearCommand::class,

            'config:cache'        => \App\Console\Commands\Config\CacheCommand::class,
            'config:clear'        => \App\Console\Commands\Config\ClearCommand::class,

            'test'               => \App\Console\Commands\TestCommand::class,
            // 'route:list'      => \App\Console\Commands\RouteListCommand::class,
            // Add more commands here as needed
        ];
    }

    /**
     * Handle console command execution
     *
     * Parses command line arguments, finds the appropriate command class,
     * and executes it with the provided arguments. Shows available commands
     * if no command is specified or command is not found.
     *
     * @param array $argv Command line arguments from CLI
     */
    public function handle(array $argv)
    {
        array_shift($argv); // remove script name
        $commandName = isset($argv[0]) ? $argv[0] : null;

        if (!$commandName || !isset($this->commands[$commandName])) {
            echo "Available commands:\n";
            foreach ($this->commands as $key => $class) {
                echo "  - {$key}\n";
            }
            return;
        }

        $class = $this->commands[$commandName];
        $command = new $class();
        $command->handle(array_slice($argv, 1));
    }
}
