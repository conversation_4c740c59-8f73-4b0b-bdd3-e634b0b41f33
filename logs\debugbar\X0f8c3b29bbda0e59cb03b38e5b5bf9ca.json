{"__meta": {"id": "X0f8c3b29bbda0e59cb03b38e5b5bf9ca", "datetime": "2025-07-10 11:08:18", "utime": 1752138498.88578, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 8, "messages": [{"message": "Route dispatched", "message_html": null, "is_string": true, "label": "info", "time": 1752138498.855198, "xdebug_link": null}, {"message": "Current Branch: HQ", "message_html": null, "is_string": true, "label": "info", "time": 1752138498.855207, "xdebug_link": null}, {"message": "Middleware: auth", "message_html": null, "is_string": true, "label": "info", "time": 1752138498.855645, "xdebug_link": null}, {"message": "Template: default", "message_html": null, "is_string": true, "label": "info", "time": 1752138498.859418, "xdebug_link": null}, {"message": "Exception: Database error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'hq2.user_profiles' doesn't exist in D:\\Projects\\Longwan\\HQ2\\src\\Core\\Database.php:498", "message_html": null, "is_string": true, "label": "error", "time": 1752138498.881978, "xdebug_link": null}, {"message": "Stack trace: #0 D:\\Projects\\Longwan\\HQ2\\src\\Core\\Database.php(504): App\\Core\\Database->getStatement()\n#1 D:\\Projects\\Longwan\\HQ2\\src\\Core\\Database.php(519): App\\Core\\Database->fetch()\n#2 D:\\Projects\\Longwan\\HQ2\\src\\Core\\Model.php(272): App\\Core\\Database->first()\n#3 D:\\Projects\\Longwan\\HQ2\\src\\Core\\Relationship.php(106): App\\Core\\Model::whereFirst('user_id', 1)\n#4 D:\\Projects\\Longwan\\HQ2\\src\\Core\\Relationship.php(85): App\\Core\\Relationship->getHasOne(1)\n#5 D:\\Projects\\Longwan\\HQ2\\src\\Core\\ModelInstance.php(72): App\\Core\\Relationship->get()\n#6 D:\\Projects\\Longwan\\HQ2\\src\\Controllers\\HomeController.php(50): App\\Core\\ModelInstance->__get('profile')\n#7 [internal function]: App\\Controllers\\HomeController->home()\n#8 D:\\Projects\\Longwan\\HQ2\\src\\Core\\Router.php(275): call_user_func_array(Array, Array)\n#9 D:\\Projects\\Longwan\\HQ2\\src\\Core\\Router.php(217): App\\Core\\Router::callAction('HomeController@...', Array)\n#10 D:\\Projects\\Longwan\\HQ2\\public\\index.php(33): App\\Core\\Router::dispatch('/home', 'GET')\n#11 {main}", "message_html": null, "is_string": true, "label": "error", "time": 1752138498.881989, "xdebug_link": null}, {"message": "Rendering template: errors/500.tpl", "message_html": null, "is_string": true, "label": "info", "time": 1752138498.88236, "xdebug_link": null}, {"message": "Checking privilege: CROSS_BRANCH for branch: 1 NO", "message_html": null, "is_string": true, "label": "info", "time": 1752138498.885589, "xdebug_link": null}]}, "request": {"$_GET": "array:1 [\n  \"url\" => \"home\"\n]", "$_POST": "[]", "$_COOKIE": "array:1 [\n  \"PHPSESSID\" => \"p1ves5s1kcdrn9949agug85s28\"\n]", "$_SESSION": "array:7 [\n  \"PHPDEBUGBAR_STACK_DATA\" => []\n  \"debugbar_enabled\" => true\n  \"current_branch\" => array:44 [\n    \"id\" => 1\n    \"rev\" => \"b78b\"\n    \"code\" => \"HQ\"\n    \"is_hq\" => 1\n    \"hq_control\" => 1\n    \"has_counter\" => 0\n    \"pair_code\" => \"fb395718-52ba-4cbb-bd76-8fefa27e1762\"\n    \"sync\" => 0\n    \"sync_up\" => 1\n    \"sync_down\" => 1\n    \"ip_address\" => \"127.0.0.1\"\n    \"last_ping\" => \"2019-09-17 10:28:59\"\n    \"description\" => \"LONGWAN HEADQUATERS\"\n    \"group\" => \"HQ\"\n    \"company_no\" => \"515064-H\"\n    \"contact_person\" => \"USER\"\n    \"phone1\" => \"04-4236533\"\n    \"phone2\" => \"04-4232198\"\n    \"fax1\" => \"asdaaaaaazzzzz\"\n    \"email\" => \"<EMAIL>\"\n    \"report_email\" => \"\"\n    \"address\" => \"\"\"\n      NO.57,JALAN SEMBILAN,\\n\n      KAWASAN PERINDUSTRIAN,\\n\n      BAKAR ARANG,\\n\n      08000 SUNGAI PETANI,\\n\n      KEDAH.\n      \"\"\"\n    \"gst_registered\" => 1\n    \"gst_register_no\" => \"000336379904\"\n    \"gst_start_date\" => \"2015-04-01\"\n    \"host\" => \"http://gmark.dyndns.org:3000\"\n    \"master_host\" => \"localhost\"\n    \"anydesk\" => \"*********\"\n    \"rustdesk\" => null\n    \"company_id\" => 3\n    \"e_invoice_enabled\" => 1\n    \"e_invoice_profile_id\" => 1\n    \"e_invoice_country\" => \"MALAYSIA\"\n    \"e_invoice_state\" => \"KEDAH\"\n    \"e_invoice_city\" => \"SUNGAI PETANI\"\n    \"e_invoice_postcode\" => \"08000\"\n    \"e_invoice_address1\" => \"NO. 57\"\n    \"e_invoice_address2\" => \"JALAN SEMBILAN\"\n    \"e_invoice_address3\" => \"KAWASAN PERIDUSTRIAN BAKAR ARANG\"\n    \"e_invoice_contact\" => \"6044246116\"\n    \"active\" => 1\n    \"deleted\" => 0\n    \"created_at\" => \"2017-01-04 14:06:01\"\n    \"updated_at\" => \"2025-06-25 02:14:33\"\n  ]\n  \"current_branch_id\" => 1\n  \"current_branch_code\" => \"HQ\"\n  \"authenticated_web\" => true\n  \"user_id_web\" => 1\n]"}, "time": {"count": 0, "start": 1752138498.832835, "end": 1752138498.886256, "duration": 0.0534210205078125, "duration_str": "53.42ms", "measures": []}, "memory": {"peak_usage": 734520, "peak_usage_str": "717KB"}, "exceptions": {"count": 0, "exceptions": []}, "Config": {"cached": "false", "app": "array:5 [\n  \"env\" => \"development\"\n  \"debug\" => \"false\"\n  \"name\" => \"HQ2 Application\"\n  \"mode\" => \"MAIN\"\n  \"default_branch\" => 1\n]", "database": "array:6 [\n  \"host\" => \"localhost\"\n  \"port\" => \"3306\"\n  \"name\" => \"hq2\"\n  \"user\" => \"root\"\n  \"pass\" => \"\"\n  \"charset\" => \"utf8mb4\"\n]", "session": "array:3 [\n  \"user_id\" => null\n  \"current_branch\" => array:44 [\n    \"id\" => 1\n    \"rev\" => \"b78b\"\n    \"code\" => \"HQ\"\n    \"is_hq\" => 1\n    \"hq_control\" => 1\n    \"has_counter\" => 0\n    \"pair_code\" => \"fb395718-52ba-4cbb-bd76-8fefa27e1762\"\n    \"sync\" => 0\n    \"sync_up\" => 1\n    \"sync_down\" => 1\n    \"ip_address\" => \"127.0.0.1\"\n    \"last_ping\" => \"2019-09-17 10:28:59\"\n    \"description\" => \"LONGWAN HEADQUATERS\"\n    \"group\" => \"HQ\"\n    \"company_no\" => \"515064-H\"\n    \"contact_person\" => \"USER\"\n    \"phone1\" => \"04-4236533\"\n    \"phone2\" => \"04-4232198\"\n    \"fax1\" => \"asdaaaaaazzzzz\"\n    \"email\" => \"<EMAIL>\"\n    \"report_email\" => \"\"\n    \"address\" => \"\"\"\n      NO.57,JALAN SEMBILAN,\\n\n      KAWASAN PERINDUSTRIAN,\\n\n      BAKAR ARANG,\\n\n      08000 SUNGAI PETANI,\\n\n      KEDAH.\n      \"\"\"\n    \"gst_registered\" => 1\n    \"gst_register_no\" => \"000336379904\"\n    \"gst_start_date\" => \"2015-04-01\"\n    \"host\" => \"http://gmark.dyndns.org:3000\"\n    \"master_host\" => \"localhost\"\n    \"anydesk\" => \"*********\"\n    \"rustdesk\" => null\n    \"company_id\" => 3\n    \"e_invoice_enabled\" => 1\n    \"e_invoice_profile_id\" => 1\n    \"e_invoice_country\" => \"MALAYSIA\"\n    \"e_invoice_state\" => \"KEDAH\"\n    \"e_invoice_city\" => \"SUNGAI PETANI\"\n    \"e_invoice_postcode\" => \"08000\"\n    \"e_invoice_address1\" => \"NO. 57\"\n    \"e_invoice_address2\" => \"JALAN SEMBILAN\"\n    \"e_invoice_address3\" => \"KAWASAN PERIDUSTRIAN BAKAR ARANG\"\n    \"e_invoice_contact\" => \"6044246116\"\n    \"active\" => 1\n    \"deleted\" => 0\n    \"created_at\" => \"2017-01-04 14:06:01\"\n    \"updated_at\" => \"2025-06-25 02:14:33\"\n  ]\n  \"login\" => false\n]", "environment": "array:4 [\n  \"PHP_VERSION\" => \"8.2.12\"\n  \"SERVER_SOFTWARE\" => \"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12\"\n  \"REQUEST_METHOD\" => \"GET\"\n  \"REQUEST_URI\" => \"/home\"\n]"}, "pdo": {"nb_statements": 5, "nb_failed_statements": 1, "accumulated_duration": 0.0018188953399658203, "memory_usage": 81008, "peak_memory_usage": 698984, "statements": [{"sql": "SELECT * FROM `branches` WHERE id = :param_0", "row_count": 1, "stmt_id": "00000000000000160000000000000000", "prepared_stmt": "SELECT * FROM `branches` WHERE id = :param_0", "params": {":param_0": "1"}, "duration": 0.0004398822784423828, "duration_str": "440μs", "memory": 19904, "memory_str": "19.44KB", "end_memory": 568552, "end_memory_str": "555.23KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `users` WHERE id = :param_0", "row_count": 1, "stmt_id": "000000000000001c0000000000000000", "prepared_stmt": "SELECT * FROM `users` WHERE id = :param_0", "params": {":param_0": "1"}, "duration": 0.00035190582275390625, "duration_str": "352μs", "memory": 17400, "memory_str": "16.99KB", "end_memory": 640424, "end_memory_str": "625.41KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `users` WHERE id = :param_0", "row_count": 1, "stmt_id": "000000000000002e0000000000000000", "prepared_stmt": "SELECT * FROM `users` WHERE id = :param_0", "params": {":param_0": "1"}, "duration": 0.00011992454528808594, "duration_str": "120μs", "memory": 17400, "memory_str": "16.99KB", "end_memory": 642288, "end_memory_str": "627.23KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_profiles` WHERE user_id = :param_0", "row_count": 0, "stmt_id": "00000000000000330000000000000000", "prepared_stmt": "SELECT * FROM `user_profiles` WHERE user_id = :param_0", "params": {":param_0": "1"}, "duration": 0.0005171298980712891, "duration_str": "517μs", "memory": 8816, "memory_str": "8.61KB", "end_memory": 635168, "end_memory_str": "620.28KB", "is_success": false, "error_code": "42S02", "error_message": "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'hq2.user_profiles' doesn't exist", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 1, "stmt_id": "000000000000004b0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": "1"}, "duration": 0.00039005279541015625, "duration_str": "390μs", "memory": 17488, "memory_str": "17.08KB", "end_memory": 698984, "end_memory_str": "682.6KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}], "accumulated_duration_str": "1.82ms", "memory_usage_str": "79.11KB", "peak_memory_usage_str": "682.6KB"}}