<?php

namespace App\Models;

use App\Core\Model;

/**
 * UserProfile Model
 *
 * Handles user profile information and settings
 */
class UserProfile extends Model
{
    /** @var string */
    protected static $table = 'user_profiles';

    /** @var array */
    protected static $fillable = [
        'user_id', 'avatar', 'bio', 'phone', 'address', 'preferences'
    ];

    // ===== RELATIONSHIPS =====

    /**
     * Get the user that owns this profile
     *
     * @return \App\Core\Relationship
     */
    public static function user()
    {
        return static::belongsTo(User::class, 'user_id', 'id');
    }
}
