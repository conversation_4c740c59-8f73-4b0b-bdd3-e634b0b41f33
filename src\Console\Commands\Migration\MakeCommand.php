<?php

namespace App\Console\Commands\Migration;

class MakeCommand
{
    public function handle(array $args): void
    {
        $name = $args[0] ?? null;

        if (!$name) {
            echo "Usage: make:migration MigrationName\n";
            return;
        }

        $timestamp = date('Y_m_d_His');
        $filename = "{$timestamp}_{$name}.php";
        $path = __DIR__ . '/../../../migrations/' . $filename;

        $template = <<<PHP
<?php

use App\Core\Database;

return new class {
    public function up(Database \$db)
    {
        // TODO: Write migration up
    }

    public function down(Database \$db)
    {
        // TODO: Write migration rollback
    }
};
PHP;

        file_put_contents($path, $template);
        echo "Created migration: {$filename}\n";
    }
}
