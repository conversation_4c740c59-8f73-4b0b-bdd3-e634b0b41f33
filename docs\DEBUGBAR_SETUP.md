# PHP Debug Bar Setup with setOpen<PERSON><PERSON>lerUrl

This document explains the complete PHP Debug Bar setup including the `setOpenHandlerUrl` configuration.

## Overview

The PHP Debug Bar has been configured with a custom open handler URL to handle AJAX requests for detailed debugging information. This allows the debug bar to fetch and display detailed data when you click on specific debug bar tabs.

## Configuration

### 1. DebugBarService Configuration

The `setOpenHandlerUrl` is configured in `src/Services/DebugBarService.php`:

```php
public static function getRenderer()
{
    if (!self::isEnabled()) {
        return null;
    }
    
    $renderer = self::$debugbar->getJavascriptRenderer();
    
    // Set the base URL for debug bar assets
    $renderer->setBaseUrl('/vendor/php-debugbar/php-debugbar/src/DebugBar/Resources');
    
    // Set the open handler URL for AJAX requests
    $renderer->setOpenHandlerUrl('/debugbar/openhandler');
    
    return $renderer;
}
```

### 2. Open Handler Controller

The open handler is implemented in `src/Controllers/DebugBarController.php`:

```php
public function openHandler()
{
    // Only allow in debug mode
    if (!DebugBarService::isEnabled()) {
        http_response_code(404);
        echo json_encode(['error' => 'Debug bar not enabled']);
        return;
    }

    $debugBar = DebugBarService::getDebugBar();
    if (!$debugBar) {
        http_response_code(404);
        echo json_encode(['error' => 'Debug bar not available']);
        return;
    }

    // Set content type to JSON
    header('Content-Type: application/json');
    
    // Handle the request based on the 'op' parameter
    $operation = $_GET['op'] ?? $_POST['op'] ?? '';
    
    try {
        switch ($operation) {
            case 'get':
                // Get data for a specific request ID
                $id = $_GET['id'] ?? $_POST['id'] ?? '';
                if (empty($id)) {
                    throw new \Exception('Missing request ID');
                }
                
                $data = $debugBar->getStorage()->get($id);
                if ($data === null) {
                    throw new \Exception('Request not found');
                }
                
                echo json_encode($data);
                break;
                
            case 'clear':
                // Clear storage
                $debugBar->getStorage()->clear();
                echo json_encode(['success' => true]);
                break;
                
            default:
                throw new \Exception('Unknown operation: ' . $operation);
        }
    } catch (\Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to handle debug bar request: ' . $e->getMessage()]);
    }
}
```

### 3. Routes Configuration

The open handler routes are defined in `routes/web.php`:

```php
// Debug Bar open handler for AJAX requests
Router::get('debugbar/openhandler', 'DebugBarController@openHandler');
Router::post('debugbar/openhandler', 'DebugBarController@openHandler');
```

### 4. Storage Configuration

File storage is configured for persistence:

```php
private static function configureStorage()
{
    if (!self::isEnabled()) {
        return;
    }
    
    // Create storage directory if it doesn't exist
    $storageDir = __DIR__ . '/../../logs/debugbar';
    if (!is_dir($storageDir)) {
        mkdir($storageDir, 0755, true);
    }
    
    // Set up file storage
    $storage = new FileStorage($storageDir);
    self::$debugbar->setStorage($storage);
}
```

## How It Works

1. **Initialization**: Debug bar is initialized in `public/index.php`
2. **Template Integration**: Debug bar renderer is assigned to all Smarty templates
3. **Asset Serving**: Debug bar CSS/JS assets are served via `DebugBarController@assets`
4. **Open Handler**: AJAX requests for detailed data are handled by `DebugBarController@openHandler`
5. **Storage**: Debug data is stored in `logs/debugbar/` directory for persistence

## Features

### Available Operations

- **get**: Retrieve detailed data for a specific request ID
- **clear**: Clear all stored debug data

### Supported Collectors

- **Messages**: Custom log messages and application events
- **Time**: Performance timing information
- **Memory**: Memory usage tracking
- **PDO**: Database query monitoring with query details
- **Config**: Application configuration display
- **Exceptions**: Exception tracking and display

### Production Usage

The debug bar can be enabled in production with:

1. **Session-based**: Visit `/debugbar/enable` to enable for your session
2. **Environment variable**: Set `APP_DEBUG=true` in environment
3. **Force enable**: Set `DEBUGBAR_FORCE_ENABLE=true` (use with caution)

## Template Usage

The debug bar is automatically included in all templates via `templates/base.tpl`:

```smarty
{if isset($debugbarRenderer)}
    {$debugbarRenderer->renderHead()}
{/if}

<!-- Page content -->

{if isset($debugbarRenderer)}
    {$debugbarRenderer->render()}
{/if}
```

## Testing

To test the open handler functionality:

1. Enable debug mode
2. Visit any page with the debug bar
3. Click on debug bar tabs to see detailed information
4. Check browser network tab for AJAX requests to `/debugbar/openhandler`

## Security Notes

- Debug bar is only enabled in development mode by default
- Open handler validates debug bar is enabled before processing requests
- File storage is restricted to `logs/debugbar/` directory
- Production access requires explicit configuration

## Troubleshooting

### Debug Bar Not Showing

1. Check `APP_DEBUG=true` in configuration
2. Verify debug bar is initialized in `public/index.php`
3. Check template includes debug bar renderer

### Open Handler Not Working

1. Verify routes are registered correctly
2. Check storage directory permissions (`logs/debugbar/`)
3. Ensure debug bar storage is configured
4. Check browser console for JavaScript errors

### Asset Loading Issues

1. Verify debug bar assets route is working
2. Check file permissions in vendor directory
3. Ensure base URL is set correctly
