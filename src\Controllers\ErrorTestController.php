<?php

namespace App\Controllers;

use App\Controller\Controller;

/**
 * Error Test Controller
 * 
 * Controller for testing the improved HTTP error handling system.
 * Only available in debug mode for security.
 */
class ErrorTestController extends Controller
{
    /**
     * Show error testing page
     */
    public function index()
    {
        // Only allow access in debug mode
        if (!config('app.debug', false)) {
            $this->abort404();
        }

        $this->view('error-test', [
            'title' => 'Error Handling Test Page'
        ]);
    }

    /**
     * Test 404 error
     */
    public function test404()
    {
        $this->abort404('This is a test 404 error.');
    }

    /**
     * Test 403 error
     */
    public function test403()
    {
        $this->abort403('This is a test 403 forbidden error.');
    }

    /**
     * Test 401 error
     */
    public function test401()
    {
        $this->abort401('This is a test 401 unauthorized error.');
    }

    /**
     * Test 500 error
     */
    public function test500()
    {
        $this->abort(500, 'This is a test 500 internal server error.');
    }

    /**
     * Test validation error
     */
    public function testValidation()
    {
        $validationErrors = [
            'email' => ['Email is required', 'Email must be valid'],
            'password' => ['Password is required', 'Password must be at least 8 characters']
        ];

        $this->abortValidation($validationErrors, 'Validation failed for the submitted data.');
    }

    /**
     * Test JSON error response
     */
    public function testJsonError()
    {
        $this->jsonError('This is a test JSON error response.', 400, [
            'field' => 'test_field',
            'value' => 'invalid_value'
        ]);
    }

    /**
     * Test JSON success response
     */
    public function testJsonSuccess()
    {
        $this->jsonSuccess([
            'message' => 'Test successful',
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => ['key' => 'value']
        ], 'Test JSON success response');
    }

    /**
     * Test exception throwing
     */
    public function testException()
    {
        throw new \Exception('This is a test unhandled exception.');
    }

    /**
     * Test database error
     */
    public function testDatabaseError()
    {
        try {
            // Try to query a non-existent table
            DB()->table('non_existent_table')->get();
        } catch (\Exception $e) {
            $this->abort(500, 'Database error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Test conditional abort
     */
    public function testConditionalAbort()
    {
        $condition = $_GET['trigger'] ?? false;
        
        $this->abortIf($condition, 400, 'Conditional abort triggered by query parameter.');
        
        $this->jsonSuccess(['message' => 'No abort condition met']);
    }
}
