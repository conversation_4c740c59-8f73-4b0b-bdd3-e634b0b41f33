{extends file="base.tpl"}


{block name="body"}
    {* Display logged-in user information *}
    {if $isLoggedIn}
        <div class="alert alert-info">
            <h4>Welcome, {$currentUser.name|default:$currentUser.username|escape}!</h4>
            <p>
                <strong>Username:</strong> {$currentUser.username|escape}<br>
                <strong>User ID:</strong> {$currentUser.id}<br>
                <strong>Status:</strong> Logged In
            </p>
        </div>
    {else}
        <div class="alert alert-warning">
            <p>You are not logged in. <a href="/login">Please log in</a>.</p>
        </div>
    {/if}

    <h1>User List</h1>
    <ul>
        {foreach from=$users item=user}
            <li>
                {$user.name|escape}
                {if $isLoggedIn && $currentUser.id == $user.id}
                    <span class="badge">You</span>
                {/if}
            </li>
        {/foreach}
    </ul>

        <pre>
        {if count($current_branch)}
            {config key='app.name' default='Not set'}
        {/if}
                {* {$current_branch|print_r} *}
        </pre>
        {* <pre>
                {$availableBranches|print_r}
        </pre> *}

        <pre>
            {$currentUser|print_r}
        </pre>

        <pre>
            {currentUser field="id"}
        </pre>

        <pre>
            {currentUser field="username"}
        </pre>


        <pre>
            {0|ifzero:"asd"}
            {1|ifzero:"asd"}
        </pre>

        {access user=$currentUser}
            access
        {/access}

        {* {access test=false}
            access
        {/access} *}
{/block}
