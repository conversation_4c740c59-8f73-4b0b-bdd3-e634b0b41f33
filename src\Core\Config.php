<?php

namespace App\Core;

class Config
{
    protected static $instance = null;
    /** @var string */
    protected static $cache_file = __DIR__ . '/../../bootstrap/data/config.php';

    protected static $config = null;

    public function __construct()
    {
        self::loadConfig();
    }

    public function get($key = '', $default = null)
    {
        // If just retrieving
        if (empty($key)) {
            return self::$config;
        }

        $segments = explode('.', $key);
        $result = self::$config;

        foreach ($segments as $segment) {
            if (is_array($result) && array_key_exists($segment, $result)) {
                $result = $result[$segment];
            } else {
                return $default;
            }
        }

        return $result;
    }

    public function set($array)
    {
        foreach ($array as $key => $value) {
            $segments = explode('.', $key);
            $ref = &self::$config;

            foreach ($segments as $segment) {
                if (!isset($ref[$segment]) || !is_array($ref[$segment])) {
                    $ref[$segment] = [];
                }
                $ref = &$ref[$segment];
            }

            $ref = $value;
        }
    }


    public static function loadConfig()
    {
        if (file_exists(self::$cache_file)) {
            self::$config = require_once self::$cache_file;

            self::$config['cached'] = true;
        } else {
            // Load environment variables
            $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__ . '/../../');
            $dotenv->safeLoad();

            // Load configuration file
            self::$config = require __DIR__ . '/../../config/config.php';
        }
    }

    public static function cache()
    {
        self::cacheClear();

        static::loadConfig();
                
        file_put_contents(self::$cache_file,
            "<?php\nreturn "
            .var_export(self::$config, true)
            .";\n"
        );
    }

    public static function cacheClear()
    {
        if (file_exists(self::$cache_file)) {
            unlink(self::$cache_file);
        }
    }

    public static function getInstance()
    {
        if(!isset(self::$instance)) {
            self::$instance = new Config();
        }

        return self::$instance;
    }
}
