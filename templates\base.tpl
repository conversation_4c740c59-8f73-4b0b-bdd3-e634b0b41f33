<!doctype html>
<html>
    <head>
        {$script_rev="?v=202506201409"}
        <base href="/" />
        <meta name="robots" content="noindex,nofollow">
        <meta name="googlebot" content="noindex,nofollow">
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{config key='app.name' default='APP'}</title>
        <link href="assets/bootstrap-3.4.1/css/bootstrap.min.css{$script_rev}" rel="stylesheet">
        <link href="assets/bootstrap-submenu/css/bootstrap-submenu.min.css{$script_rev}" rel="stylesheet">
        <link href="assets/bootstrap-modal/css/bootstrap-modal.css{$script_rev}" rel="stylesheet">
        <link href="assets/bootstrap-modal/css/bootstrap-modal-bs3patch.css{$script_rev}" rel="stylesheet">
        <link href="assets/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css{$script_rev}" rel="stylesheet">
        <link href="assets/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet">
        <link href="assets/css/style.css{$script_rev}" rel="stylesheet">
        <link href="assets/css/style2.css{$script_rev}" rel="stylesheet">
        {block name="header"}{/block}

        {if isset($debugbarRenderer)}
            {$debugbarRenderer->renderHead()}
        {/if}
    </head>
    <body>
        {if $isLoggedIn}
            {include file="menu.tpl"}
        {/if}

        {* Simple navigation bar with user info *}
        {* {if $isLoggedIn}
            <nav class="navbar navbar-default">
                <div class="container-fluid">
                    <div class="navbar-header">
                        <a class="navbar-brand" href="/home">{$currentBranch.code}</a>
                    </div>
                    <div class="navbar-collapse">
                        <ul class="nav navbar-nav navbar-right">
                        <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    <span class="glyphicon glyphicon-user"></span>
                                    {$currentUser.name|default:$currentUser.username|escape}
                                    <span class="caret"></span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="/logout">Logout</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        {/if} *}

        <div class="container-fluid body-content">
            {block name="body"}{/block}
        </div>

        <script src="assets/jquery-3.5.1.min.js{$script_rev}"></script>
        <script src="assets/jquery-ui.min.js{$script_rev}"></script>
        <script src="assets/bootstrap-3.4.1/js/bootstrap.min.js{$script_rev}"></script>
        <script src="assets/bootstrap-submenu/js/bootstrap-submenu.min.js{$script_rev}"></script>
        <script src="assets/bootstrap-modal/js/bootstrap-modalmanager.js{$script_rev}"></script>
        <script src="assets/bootstrap-modal/js/bootstrap-modal.js{$script_rev}"></script>
        <script src="assets/bootstrap-datetimepicker/js/moment.min.js{$script_rev}"></script>
        <script src="assets/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js{$script_rev}"></script>
        <script src="assets/jquery.autocomplete.min.js{$script_rev}"></script>
        <script src="assets/bootstrap-extends.js{$script_rev}"></script>
        <script src="assets/functions.js{$script_rev}"></script>
        {block name="footer"}{/block}

        {if isset($debugbarRenderer)}
            {$debugbarRenderer->render()}
        {/if}
    </body>
</html>
