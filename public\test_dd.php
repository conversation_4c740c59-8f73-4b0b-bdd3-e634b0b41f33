<?php
/**
 * Browser test for dd-like debugging functions
 * 
 * Access this file via web browser to see the beautiful dd() output
 */

require_once '../vendor/autoload.php';
require_once '../src/helper.php';

// Test data
$testString = "Hello, World! This is a test string with some length to see how it displays.";
$testArray = [
    'name' => '<PERSON>',
    'age' => 30,
    'email' => '<EMAIL>',
    'hobbies' => ['reading', 'coding', 'gaming'],
    'address' => [
        'street' => '123 Main St',
        'city' => 'Anytown',
        'country' => 'USA',
        'coordinates' => [
            'lat' => 40.7128,
            'lng' => -74.0060
        ]
    ],
    'active' => true,
    'balance' => 1234.56,
    'notes' => null
];

$testObject = new stdClass();
$testObject->name = 'Test Object';
$testObject->value = 42;
$testObject->active = true;
$testObject->data = ['key1' => 'value1', 'key2' => 'value2'];

class TestClass {
    public $publicProperty = 'public value';
    protected $protectedProperty = 'protected value';
    private $privateProperty = 'private value';
    
    public function __construct() {
        $this->publicProperty = 'initialized public';
    }
    
    public function testMethod() {
        return 'test result';
    }
}

$testClassInstance = new TestClass();

// Choose which test to run based on URL parameter
$test = $_GET['test'] ?? 'dd';

switch ($test) {
    case 'dd':
        echo '<h1>Testing dd() function</h1>';
        echo '<p>This will dump the variables and stop execution.</p>';
        dd($testArray, $testObject, $testClassInstance);
        break;
        
    case 'dump':
        echo '<h1>Testing dump() function</h1>';
        echo '<p>This will dump variables without stopping execution.</p>';
        
        // Note: Our custom dump might conflict with Symfony's dump
        // Let's use a different approach
        echo '<h2>String:</h2>';
        dd($testString);
        break;
        
    case 'ddd':
        echo '<h1>Testing ddd() function (Enhanced Debug)</h1>';
        echo '<p>This will show enhanced debug information including memory usage and request details.</p>';
        ddd($testArray);
        break;
        
    case 'ray':
        echo '<h1>Testing ray() function</h1>';
        echo '<p>Ray function logs to debug bar and returns the value for chaining.</p>';
        
        // Test ray chaining
        $result = ray($testArray)['name'];
        echo '<p>Ray result (chained): ' . htmlspecialchars($result) . '</p>';
        
        ray("This is a ray message", $testObject, $testArray);
        
        echo '<p>Check the debug bar for logged messages!</p>';
        echo '<p><a href="?test=dd">Test dd()</a> | <a href="?test=ddd">Test ddd()</a> | <a href="?test=multiple">Test Multiple Variables</a></p>';
        break;
        
    case 'multiple':
        echo '<h1>Testing Multiple Variables</h1>';
        echo '<p>This will dump multiple variables at once.</p>';
        dd($testString, $testArray, $testObject, $testClassInstance, true, false, null, 123, 45.67);
        break;
        
    case 'formatBytes':
        echo '<h1>Testing formatBytes() helper</h1>';
        echo '<p>Memory formatting examples:</p>';
        echo '<ul>';
        echo '<li>1024 bytes = ' . formatBytes(1024) . '</li>';
        echo '<li>1048576 bytes = ' . formatBytes(1048576) . '</li>';
        echo '<li>1073741824 bytes = ' . formatBytes(1073741824) . '</li>';
        echo '<li>Current memory usage: ' . formatBytes(memory_get_usage(true)) . '</li>';
        echo '<li>Peak memory usage: ' . formatBytes(memory_get_peak_usage(true)) . '</li>';
        echo '</ul>';
        echo '<p><a href="?test=dd">Test dd()</a> | <a href="?test=ray">Test ray()</a></p>';
        break;
        
    default:
        echo '<h1>DD-Like Functions Test Page</h1>';
        echo '<p>Choose a test to run:</p>';
        echo '<ul>';
        echo '<li><a href="?test=dd">Test dd() - Dump and Die</a></li>';
        echo '<li><a href="?test=ddd">Test ddd() - Enhanced Debug</a></li>';
        echo '<li><a href="?test=ray">Test ray() - Chaining Debug</a></li>';
        echo '<li><a href="?test=multiple">Test Multiple Variables</a></li>';
        echo '<li><a href="?test=formatBytes">Test formatBytes() Helper</a></li>';
        echo '</ul>';
        
        echo '<h2>Features</h2>';
        echo '<ul>';
        echo '<li><strong>dd()</strong> - Beautiful dump with syntax highlighting, stops execution</li>';
        echo '<li><strong>ddd()</strong> - Enhanced debug with memory usage, timing, and request info</li>';
        echo '<li><strong>ray()</strong> - Logs to debug bar and returns value for chaining</li>';
        echo '<li><strong>formatBytes()</strong> - Human-readable byte formatting</li>';
        echo '</ul>';
        
        echo '<h2>Sample Data</h2>';
        echo '<p>The test uses the following sample data:</p>';
        echo '<pre>';
        echo htmlspecialchars(print_r([
            'string' => $testString,
            'array' => $testArray,
            'object' => $testObject,
            'class_instance' => 'TestClass instance'
        ], true));
        echo '</pre>';
        
        echo '<h2>Styling</h2>';
        echo '<p>The dd/dump functions include:</p>';
        echo '<ul>';
        echo '<li>Dark theme with syntax highlighting</li>';
        echo '<li>Color-coded variable types</li>';
        echo '<li>Proper indentation for nested structures</li>';
        echo '<li>Monospace font for better readability</li>';
        echo '<li>File and line number information</li>';
        echo '<li>Stack trace for debugging</li>';
        echo '</ul>';
        
        break;
}
?>
