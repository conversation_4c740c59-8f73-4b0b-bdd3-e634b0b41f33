body{
	padding-top: 60px;
}

input[readonly], input[disabled], select[disabled], textarea[disabled]{
	cursor: not-allowed;
}

.datatable {
	border:1px solid #ddd;
	min-width:100% !important;
	table-layout: fixed;
}

.datatable > table {
	border:1px solid #ddd;
}

.datatable > tbody tr:hover {
	background:#c7c7c7 !important;
}

.datatable td, .datatable th {
	padding:5px;
	border:1px solid #ddd;
}

.datatable tr td a img {
	width:20px;
}

.datatable tr td .img-thumbnail {
	width:auto !important;
}

.dataform {
	border-collapse: separate;
	border-spacing: 5px;
	width:100%;
}

.dataform tr { vertical-align:top; }

.dataform td input[type="text"], .dataform td select, .dataform td textarea {
	width:100%;
}

.dataform td, .dataform th { padding: 0 0 0 10px; vertical-align: middle; }
.dataform .input{ padding: 0.4em; }
.dataform input[disabled]{ cursor: not-allowed; }
.dataform textarea{ height:auto; }
.dataform input[type="submit"], .dataform input[type="button"]{ width:100px; }

#item-list{
	table-layout: fixed;
	font-size: 16px;
	min-width: 100% !important;
	border-color: #a5a4a4;
}

#item-list > thead th, #item-list > tfoot th{
	padding: 5px;
	background-color: #dddada;
	color: #585858;
	border-color: #a5a4a4;
}

#item-list > tbody th, #item-list > tbody td{
	-webkit-box-sizing: initial;
	-moz-box-sizing: initial;
	box-sizing: initial;
	border-color: #a5a4a4;
	white-space: nowrap;
}

#item-list > tbody tr.row_item:hover > td, #item-list > tbody tr.row_item:hover + tr.row_item_extend{
	border-top: 1px red solid !important;
	border-bottom: 1px red solid !important;
	background:#c7c7c7 !important;
}

#item-list > tbody tr:nth-child(1):hover input[readonly],
#item-list > tbody tr:nth-child(1):hover select[disabled]{
	background:#c7c7c7 !important;
}

#item-list > tbody tr.highlighted,
#item-list > tbody tr.highlighted input[readonly],
#item-list > tbody tr.highlighted select[disabled]{
	background: #ffb3b3 !important;
}

#item-list > tbody input, #item-list > tbody select,
#item-list > tfoot input, #item-list > tfoot select{
	background-color: #c1eaff;
	box-shadow: none;
	border: 0px;
	margin:0;
	width: 100% !important;
	border-radius: 0px;
	outline: none;
}

#item-list > tbody input, #item-list > tfoot input{
	padding: 4px;
}

#item-list > tbody select, #item-list > tfoot select{
	padding: 5px;
	-moz-appearance: listbox;
	-webkit-appearance: menulist;
	background: none #c1eaff !important;
	color: #000 !important;
}

#item-list > tbody input[readonly], #item-list > tfoot input[readonly]{
	background-color: #fff;
	box-shadow:none;
	border: none !important;
	color: blue;
}

#item-list > tbody input[disabled], #item-list > tfoot input[disabled]{
	background-color: #fff;
	border: none !important;
}

#item-list > tbody select[disabled], #item-list > tfoot select[disabled]{
	background-color: #fff !important;
	-moz-appearance: none;
	-webkit-appearance: none;
	border: none !important;
}

#item-list img {
	width:20px;
}

#item-list .cost{
	text-align: right;
	color: #000;
	min-width: 85px !important;
	max-width: 100% !important;
}

#item-list .price{
	text-align: right;
	color: #000;
	min-width: 85px !important;
	max-width: 100% !important;
}

#item-list .qty{
	text-align: center;
	min-width: 50px !important;
	max-width: 100% !important;
}

#item-list .discount{
	width: 60px !important;
	min-width: 65px !important;
	max-width: 100% !important;
}

#item-list .service_charge{
	width: 60px !important;
	min-width: 65px !important;
	max-width: 100% !important;
}

#item-list .date{
	color: #000;
	min-width: 80px !important;
	max-width: 100% !important;
}

#item-list .invoice_no{
	color: #000;
	min-width: 200px !important;
	max-width: 100% !important;
}

#item-list .idx{
	text-align: right;
}

#item-list .inactive{
	background: #ff8000 !important;
	border-top: 2px #ff8000 solid !important;
	border-bottom: 2px #ff8000 solid !important;
}

#item-list .row-sku_id{
	padding:0px 2px;
	display: block;
	color: blue !important;
	font-size: 14px !important;
	min-width: 75px !important;
	max-width: 100% !important;
}

#item-list .row-barcode{
	padding:0px 2px;
	display: block;
	color: blue !important;
	font-size: 14px !important;
	min-width: 120px !important;
	max-width: 100% !important;
}

#item-list .row-line{
	padding:0px 2px;
	display: block;
	font-size: 14px !important;
	color: black !important;
}

#item-list .row-department{
	padding:0px 2px;
	display: block;
	font-size: 14px !important;
	color: black !important;
}

#item-list .row-category{
	padding:0px 2px;
	display: block;
	font-size: 14px !important;
	color: black !important;
}

#item-list .row-description{
	padding:0px 2px;
	display: block;
	font-size: 14px !important;
	color: black !important;
}

#item-list .row-uom{
	font-size: 14px !important;
	min-width: 70px !important;
	max-width: 100% !important;
}

#item-list .var_short{
	color: red !important;
	font-weight: bold;
}

#item-list .var_over{
	color: black !important;
	font-weight: bold;
}

#item-list .over_price {
	background:#f00 !important;
	color:#fff !important;
}

#item-list .lower_cost {
	background:#18CC08 !important;
	color:#fff !important;
}

#item-list .over_cost {
	background:#f00 !important;
	color:#fff !important;
}

.uom_cost_price{
	border-color: #ddd;
}

.uom_cost_price > thead th{
	padding: 5px;

}

.search_loader{
	position: absolute;
	right:10px;
	margin-top:7px;
}

#tools{
	position: fixed;
	bottom: 0;
	left:0;
	right:0;
	text-align: center;
	width: 100%;
	background: #808080;
	padding:5px;
	margin:0;
}

#doc-history{
	font-size: 11px;
	width:100%;
}

#doc-history ul{
	overflow: auto;
	max-height:200px;
	list-style: none;
	margin: 0;
	padding:0;
}

#doc-history li{
	margin: 0;
	border-bottom: 1px dotted #ADA9A9;
}

.table-stock{
	table-layout: fixed;
	font-size: 16px;
	border-color: #ddd;
	overflow: hidden;
}

.table-stock th{
	padding: 5px;
}

.table-stock th, .table-stock td{
	-webkit-box-sizing: initial;
	-moz-box-sizing: initial;
	box-sizing: initial;
	border-color: #ddd;
	white-space: nowrap;
	position: relative;
	padding:3px;
}

.table-stock tbody tr:hover > td{
	background:#c7c7c7 !important;
}

/*
.table-stock td:hover::after,
.table-stock th:hover::after {
  content: "";
  position: absolute;
  background-color: #c7c7c7;
  left: 0;
  top: -5000px;
  height: 10000px;
  width: 100%;
  z-index: -1;
}
*/

.autocomplete-suggestions { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; border: 1px solid #999; background: #FFF; cursor: default; overflow: auto; -webkit-box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64); -moz-box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64); box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64); }
.autocomplete-suggestion { padding: 2px 5px; white-space: nowrap; overflow: hidden; }
.autocomplete-no-suggestion { padding: 2px 5px;}
.autocomplete-selected { background: #588ED8; color: #fff !important; }
.autocomplete-suggestions strong { font-weight: bold; color: #000; }
.autocomplete-group { padding: 2px 5px; font-weight: bold; font-size: 16px; color: #000; display: block; border-bottom: 1px solid #000; }

.text-capitalize {
  text-transform: capitalize;
}

.inactive{
	background: #ff8000 !important;
	border-top: 2px #ff8000 solid !important;
	border-bottom: 2px #ff8000 solid !important;
}

.item-list{
	table-layout: fixed;
	font-size: 16px;
	min-width: 100% !important;
	border-color: #ddd;
}

.item-list table{
	border-color: #ddd;
}

.item-list th{
	padding: 5px;
}

.item-list th, .item-list td{
	-webkit-box-sizing: initial;
	-moz-box-sizing: initial;
	box-sizing: initial;
	border-color: #ddd;
	white-space: nowrap;
}

.item-list thead tr, .item-list tfoot tr{
	background-color: #201465;
	color: #eee;
}

.item-list tbody tr.row_item:hover > td{
	border-top: 1px red solid !important;
	border-bottom: 1px red solid !important;
	background:#c7c7c7 !important;
}

.item-list tbody tr.row_item:hover + tr.row_item_extend{
	border-top: 1px red solid !important;
	border-bottom: 1px red solid !important;
	background:#c7c7c7 !important;
}

.item-list tbody tr:hover > td > input[readonly],
.item-list tbody tr:hover > td > select[disabled],
.item-list tbody tr.highlighted:hover > td > input[readonly],
.item-list tbody tr.highlighted:hover > td > select[disabled]{
	background:#c7c7c7 !important;
}

.item-list tbody tr.highlighted,
.item-list tbody tr.highlighted > td > input[readonly],
.item-list tbody tr.highlighted > td > select[disabled]{
	background: #ffb3b3 !important;
}

.item-list input, .item-list select{
	background-color: #c1eaff;
	box-shadow: none;
	border: 0px solid #808080 !important;
	margin:0;
	width: 100% !important;
	border-radius: 0px;
}

.item-list input{
	padding: 4px;
}

.item-list select{
	padding: 3px;
	-moz-appearance: listbox;
	-webkit-appearance: menulist;
	background: none #c1eaff !important;
	color: #000 !important;
}

.item-list input[readonly]{
	background-color: #fff;
	box-shadow:none;
	border: none !important;
	color: blue;
}

.item-list input[disabled]{
	background-color: #fff;
	border: none !important;
}

.item-list select[disabled]{
	background-color: #fff !important;
	-moz-appearance: none;
	-webkit-appearance: none;
	border: none !important;
}

.item-list img {
	width:20px;
}

.item-list .cost{
	text-align: right;
	color: #000;
	min-width: 85px !important;
	max-width: 100% !important;
}

.item-list .price{
	text-align: right;
	color: #000;
	min-width: 85px !important;
	max-width: 100% !important;
}

.item-list .qty{
	text-align: center;
	min-width: 50px !important;
	max-width: 100% !important;
}

.item-list .discount{
	width: 60px !important;
	min-width: 65px !important;
	max-width: 100% !important;
}

.item-list .date{
	color: #000;
	min-width: 80px !important;
	max-width: 100% !important;
}

.item-list .invoice_no{
	color: #000;
	min-width: 200px !important;
	max-width: 100% !important;
}

.item-list .idx{
	text-align: right;
}

.item-list .inactive{
	background: #ff8000 !important;
	border-top: 2px #ff8000 solid !important;
	border-bottom: 2px #ff8000 solid !important;
}

.item-list .row-sku_id{
	padding:0px 2px;
	display: block;
	color: blue !important;
	font-size: 14px !important;
	min-width: 75px !important;
	max-width: 100% !important;
}

.item-list .row-barcode{
	padding:0px 2px;
	display: block;
	color: blue !important;
	font-size: 14px !important;
	min-width: 120px !important;
	max-width: 100% !important;
}

.item-list .row-line{
	padding:0px 2px;
	display: block;
	font-size: 14px !important;
	color: black !important;
}

.item-list .row-department{
	padding:0px 2px;
	display: block;
	font-size: 14px !important;
	color: black !important;
}

.item-list .row-category{
	padding:0px 2px;
	display: block;
	font-size: 14px !important;
	color: black !important;
}

.item-list .row-description{
	padding:0px 2px;
	display: block;
	font-size: 14px !important;
	color: black !important;
}

.item-list .row-uom{
	font-size: 14px !important;
	min-width: 70px !important;
	max-width: 100% !important;
}

.item-list .var_short{
	color: red !important;
	font-weight: bold;
}

.item-list 	.var_over{
	color: black !important;
	font-weight: bold;
}

.item-list 	.over_price {
	background:#f00 !important;
	color:#fff !important;
}

.item-list 	.lower_cost {
	background:#18CC08 !important;
	color:#fff !important;
}

.item-list 	.over_cost {
	background:#f00 !important;
	color:#fff !important;
}

.modal-backdrop {
	background:#c0c0c0 !important;
}

.promotion{
    background: #80ff80 !important;
}

.promotion-1{
    background: #80ff80 !important;
}

.promotion-1 input[readonly]{
    background: #80ff80 !important;
}

.promotion-2{
    background: #e6d604 !important;
}

.promotion-2 input[readonly]{
    background: #e6d604 !important;
}

.promotion-3{
    background: #04b1e6 !important;
}

.promotion-3 input[readonly]{
    background: #04b1e6 !important;
}

.promotion-4{
    background: #f37272 !important;
}

.promotion-4 input[readonly]{
    background: #f37272 !important;
}