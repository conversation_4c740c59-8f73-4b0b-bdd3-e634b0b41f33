# Logged-in User in Smarty Templates

This document explains how to access logged-in user information in Smarty templates.

## Overview

The Template class automatically assigns logged-in user data to all Smarty templates when a user is authenticated. This makes user information easily accessible throughout your application's templates.

## Available Variables

### `$isLoggedIn`
A boolean variable that indicates whether a user is currently logged in.

```smarty
{if $isLoggedIn}
    <p>User is logged in</p>
{else}
    <p>User is not logged in</p>
{/if}
```

### `$currentUser`
An array containing the current user's information. Available when `$isLoggedIn` is true.

```smarty
{if $isLoggedIn}
    <p>Welcome, {$currentUser.name|escape}!</p>
    <p>Username: {$currentUser.username|escape}</p>
    <p>User ID: {$currentUser.id}</p>
{/if}
```

## Helper Function

### `{currentUser}`
A Smarty function that provides access to user data with optional field parameter.

```smarty
{* Get all user data *}
{assign var="user" value={currentUser}}

{* Get specific field *}
<p>Username: {currentUser field="username"}</p>
<p>Name: {currentUser field="name"}</p>
<p>ID: {currentUser field="id"}</p>
```

## Usage Examples

### Basic User Information Display
```smarty
{if $isLoggedIn}
    <div class="user-info">
        <h4>Welcome, {$currentUser.name|default:$currentUser.username|escape}!</h4>
        <p>You are logged in as: {$currentUser.username|escape}</p>
    </div>
{else}
    <div class="login-prompt">
        <p><a href="/login">Please log in</a> to continue.</p>
    </div>
{/if}
```

### Navigation with User Menu
```smarty
{if $isLoggedIn}
    <nav class="navbar">
        <ul class="nav">
            <li class="dropdown">
                <a href="#" class="dropdown-toggle">
                    {$currentUser.name|default:$currentUser.username|escape}
                </a>
                <ul class="dropdown-menu">
                    <li><a href="/profile">Profile</a></li>
                    <li><a href="/logout">Logout</a></li>
                </ul>
            </li>
        </ul>
    </nav>
{/if}
```

### Conditional Content Based on User
```smarty
{if $isLoggedIn}
    {foreach from=$users item=user}
        <li>
            {$user.name|escape}
            {if $currentUser.id == $user.id}
                <span class="badge">You</span>
            {/if}
        </li>
    {/foreach}
{/if}
```

### Using the Helper Function
```smarty
{* Check if user is logged in using helper *}
{if {currentUser}}
    <p>Current user ID: {currentUser field="id"}</p>
    <p>Current username: {currentUser field="username"}</p>
{/if}
```

## Security Notes

1. Always use the `|escape` modifier when displaying user data to prevent XSS attacks
2. The user data is automatically refreshed from the database when needed
3. User authentication is checked on every template render

## Implementation Details

The logged-in user functionality is implemented in:
- `src/Core/Template.php` - Main implementation
- `src/Controllers/AuthController.php` - Session management
- `src/Middleware/AuthMiddleware.php` - Authentication checking

The system uses PHP sessions to store user authentication state and retrieves full user data from the database as needed.
