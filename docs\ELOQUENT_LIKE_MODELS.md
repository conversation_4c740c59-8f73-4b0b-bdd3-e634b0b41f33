# Eloquent-Like Models

This document explains the Laravel-style Eloquent functionality added to the application models.

## Overview

The application now includes a base `Model` class that provides Laravel-like Eloquent functionality:
- Static methods for database operations (`find()`, `create()`, `where()`, etc.)
- Automatic table name resolution
- Mass assignment protection with fillable/guarded
- Automatic timestamps
- Query builder integration

## Base Model Class

All models can extend `App\Core\Model` to get Eloquent-like functionality:

```php
use App\Core\Model;

class User extends Model
{
    protected static $table = 'users';
    protected static $primaryKey = 'id';
    protected static $fillable = ['name', 'username', 'email', 'password'];
    protected static $guarded = ['id'];
    protected static $timestamps = true;
}
```

## Configuration Properties

### Table Name
```php
protected static $table = 'users';
```
If not specified, automatically generates from class name (e.g., `User` → `users`)

### Primary Key
```php
protected static $primaryKey = 'id';
```
Default is `'id'`. Change if your table uses a different primary key.

### Mass Assignment Protection
```php
// Only these fields can be mass assigned
protected static $fillable = ['name', 'username', 'email', 'password'];

// These fields are protected from mass assignment
protected static $guarded = ['id', 'created_at', 'updated_at'];
```

### Timestamps
```php
protected static $timestamps = true;
```
Automatically adds `created_at` and `updated_at` timestamps when creating/updating records.

## Static Methods

### Finding Records

#### find($id)
Find a record by primary key:
```php
$user = User::find(1);
// Returns: array|null
```

#### findOrFail($id)
Find a record or throw exception:
```php
$user = User::findOrFail(1);
// Returns: array
// Throws: Exception if not found
```

#### findMany($ids)
Find multiple records by primary keys:
```php
$users = User::findMany([1, 2, 3]);
// Returns: array of records
```

#### all()
Get all records:
```php
$users = User::all();
// Returns: array of all records
```

#### first()
Get first record:
```php
$user = User::first();
// Returns: array|null
```

#### latest()
Get latest record (by primary key):
```php
$user = User::latest();
// Returns: array|null
```

#### oldest()
Get oldest record (by primary key):
```php
$user = User::oldest();
// Returns: array|null
```

### Querying Records

#### where($column, $operator, $value)
Start a where query:
```php
// Simple where
$users = User::where('active', 1)->get();

// With operator
$users = User::where('age', '>', 18)->get();

// Chain multiple conditions
$users = User::where('active', 1)
    ->where('role', 'admin')
    ->orderBy('name')
    ->get();
```

#### whereFirst($column, $operator, $value)
Get first record matching condition:
```php
$user = User::whereFirst('username', 'admin');
// Returns: array|null
```

### Creating Records

#### create($data)
Create a new record:
```php
$success = User::create([
    'name' => 'John Doe',
    'username' => 'johndoe',
    'email' => '<EMAIL>',
    'password' => 'secret123'
]);
// Returns: bool
```

#### createMany($records)
Create multiple records:
```php
$success = User::createMany([
    ['name' => 'John', 'username' => 'john'],
    ['name' => 'Jane', 'username' => 'jane']
]);
// Returns: bool
```

### Updating Records

#### updateById($id, $data)
Update a record by primary key:
```php
$success = User::updateById(1, [
    'name' => 'John Updated',
    'email' => '<EMAIL>'
]);
// Returns: bool
```

### Deleting Records

#### destroy($id)
Delete a record by primary key:
```php
$success = User::destroy(1);
// Returns: bool
```

### Utility Methods

#### count()
Count total records:
```php
$count = User::count();
// Returns: int
```

#### upsert($data)
Insert or update a record:
```php
$success = User::upsert([
    'id' => 1,
    'name' => 'John Updated',
    'email' => '<EMAIL>'
]);
// Returns: bool
```

## User Model Example

The `User` model extends the base `Model` class and adds user-specific functionality:

```php
class User extends Model
{
    protected static $table = 'users';
    protected static $fillable = [
        'name', 'username', 'email', 'password', 'active', 'role'
    ];

    // Override create to hash passwords
    public static function create(array $data)
    {
        if (isset($data['password'])) {
            $data['password'] = md5($data['password']);
        }
        return parent::create($data);
    }

    // User-specific methods
    public static function findByUsername($username)
    {
        return static::whereFirst('username', $username);
    }

    public static function active()
    {
        return static::where('active', 1)->get();
    }
}
```

## Usage Examples

### Basic CRUD Operations
```php
// Create
$user = User::create([
    'name' => 'John Doe',
    'username' => 'johndoe',
    'password' => 'secret123'
]);

// Read
$user = User::find(1);
$users = User::all();
$admin = User::findByUsername('admin');

// Update
User::updateById(1, ['name' => 'John Updated']);

// Delete
User::destroy(1);
```

### Query Building
```php
// Simple queries
$activeUsers = User::where('active', 1)->get();
$admins = User::where('role', 'admin')->get();

// Complex queries
$users = User::where('created_at', '>', '2023-01-01')
    ->where('active', 1)
    ->orderBy('name')
    ->limit(10)
    ->get();

// Count with conditions
$activeCount = User::where('active', 1)->count();
```

### Mass Operations
```php
// Create multiple users
User::createMany([
    ['name' => 'User 1', 'username' => 'user1'],
    ['name' => 'User 2', 'username' => 'user2'],
    ['name' => 'User 3', 'username' => 'user3']
]);

// Find multiple users
$users = User::findMany([1, 2, 3, 4, 5]);
```

## Creating New Models

To create a new model with Eloquent-like functionality:

1. **Create the model class:**
```php
<?php

namespace App\Models;

use App\Core\Model;

class Product extends Model
{
    protected static $table = 'products';
    protected static $fillable = ['name', 'price', 'category_id', 'active'];
    protected static $guarded = ['id'];
    protected static $timestamps = true;
}
```

2. **Use the model:**
```php
// Create a product
Product::create([
    'name' => 'Laptop',
    'price' => 999.99,
    'category_id' => 1
]);

// Find products
$product = Product::find(1);
$products = Product::where('active', 1)->get();
$expensive = Product::where('price', '>', 500)->get();
```

## Benefits

### Developer Experience
- **Familiar Syntax**: Laravel-like API for easy adoption
- **Less Boilerplate**: Reduces repetitive database code
- **Consistent Interface**: Same methods across all models
- **Type Safety**: Better IDE support and error detection

### Security
- **Mass Assignment Protection**: Fillable/guarded fields prevent unwanted data
- **Automatic Timestamps**: Consistent created_at/updated_at handling
- **Query Builder Integration**: Leverages existing security features

### Performance
- **Efficient Queries**: Uses optimized database query builder
- **Batch Operations**: Support for bulk inserts and updates
- **Connection Reuse**: Leverages existing database connection pooling

## Migration from Instance Methods

### Before (Instance-based)
```php
$userModel = new User();
$user = $userModel->getById(1);
$users = $userModel->getAllUsers();
```

### After (Static methods)
```php
$user = User::find(1);
$users = User::all();
```

## Advanced Usage

### Custom Query Scopes
Add custom query methods to your models:
```php
class User extends Model
{
    public static function admins()
    {
        return static::where('role', 'admin');
    }

    public static function recent()
    {
        return static::where('created_at', '>', date('Y-m-d', strtotime('-30 days')));
    }
}

// Usage
$recentAdmins = User::admins()->where('created_at', '>', '2023-01-01')->get();
```

### Model Events
Override methods to add custom behavior:
```php
class User extends Model
{
    public static function create(array $data)
    {
        // Custom validation
        if (empty($data['username'])) {
            throw new Exception('Username is required');
        }

        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        return parent::create($data);
    }
}
```

## Testing

Test the functionality using the provided test page:
- Visit `/test_user_model.php` in your browser
- See live examples of all static methods
- Test with your actual database data

## Compatibility

- **PHP Version**: 7.4+
- **Database**: MySQL/MariaDB with existing Database class
- **Existing Code**: Fully backward compatible with instance methods
- **Debug Bar**: Integrates with existing debug bar functionality
