<?php
/* Smarty version 5.5.1, created on 2025-07-10 08:08:10
  from 'file:errors/500.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.5.1',
  'unifunc' => 'content_686f58ca88e9f2_73762922',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '4e4ab09838d134d243875714d5440e38b131f699' => 
    array (
      0 => 'errors/500.tpl',
      1 => 1751602528,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
))) {
function content_686f58ca88e9f2_73762922 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates\\errors';
$_smarty_tpl->getInheritance()->init($_smarty_tpl, true);
?>


<?php 
$_smarty_tpl->getInheritance()->instanceBlock($_smarty_tpl, 'Block_863167879686f58ca8793a7_66256253', "title");
?>


<?php 
$_smarty_tpl->getInheritance()->instanceBlock($_smarty_tpl, 'Block_1471452214686f58ca87ee87_73265884', "body");
?>

<?php $_smarty_tpl->getInheritance()->endChild($_smarty_tpl, "../base.tpl", $_smarty_current_dir);
}
/* {block "title"} */
class Block_863167879686f58ca8793a7_66256253 extends \Smarty\Runtime\Block
{
public function callBlock(\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates\\errors';
?>
500 - Internal Server Error<?php
}
}
/* {/block "title"} */
/* {block "body"} */
class Block_1471452214686f58ca87ee87_73265884 extends \Smarty\Runtime\Block
{
public function callBlock(\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates\\errors';
?>

<div class="container">
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="error-page text-center">
                <div class="error-code">
                    <h1 style="font-size: 120px; color: #dc3545; margin: 0;">500</h1>
                </div>
                
                <div class="error-content">
                    <h2 class="error-title" style="margin: 20px 0;">Internal Server Error</h2>
                    
                    <p class="error-message" style="font-size: 18px; color: #666; margin: 20px 0;">
                        <?php if ($_smarty_tpl->getValue('error_message')) {?>
                            <?php echo $_smarty_tpl->getValue('error_message');?>

                        <?php } else { ?>
                            Something went wrong on our end. We're working to fix it.
                        <?php }?>
                    </p>
                    
                    <div class="error-actions" style="margin: 30px 0;">
                        <a href="/" class="btn btn-primary btn-lg">
                            <i class="fa fa-home"></i> Go Home
                        </a>
                        <button onclick="location.reload()" class="btn btn-warning btn-lg">
                            <i class="fa fa-refresh"></i> Try Again
                        </button>
                        <button onclick="history.back()" class="btn btn-default btn-lg">
                            <i class="fa fa-arrow-left"></i> Go Back
                        </button>
                    </div>
                </div>
                
                <?php ob_start();
echo $_smarty_tpl->getSmarty()->getFunctionHandler('config')->handle(array('key'=>'app.debug','default'=>false), $_smarty_tpl);
$_prefixVariable1 = ob_get_clean();
if ($_prefixVariable1 && $_smarty_tpl->getValue('debug_info')) {?>
                <div class="debug-info" style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 30px; text-align: left;">
                    <h4>Debug Information</h4>
                    <p><strong>File:</strong> <?php echo $_smarty_tpl->getValue('debug_info')['file'];?>
</p>
                    <p><strong>Line:</strong> <?php echo $_smarty_tpl->getValue('debug_info')['line'];?>
</p>
                    <details>
                        <summary><strong>Stack Trace</strong></summary>
                        <pre style="white-space: pre-wrap; font-size: 12px;"><?php echo $_smarty_tpl->getValue('debug_info')['trace'];?>
</pre>
                    </details>
                </div>
                <?php }?>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 60px 0;
}

.error-code h1 {
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-title {
    color: #333;
    font-weight: 300;
}

.error-message {
    line-height: 1.6;
}

.error-actions .btn {
    margin: 0 10px;
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 80px;
    }
    
    .error-actions .btn {
        display: block;
        margin: 10px auto;
        width: 200px;
    }
}
</style>
<?php
}
}
/* {/block "body"} */
}
