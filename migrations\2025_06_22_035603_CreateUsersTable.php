<?php

use App\Core\Database;

return new class {
    public function up(Database $db)
    {
        $db->raw("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                username VARCHAR(100) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL
            );
        ");
    }

    public function down(Database $db)
    {
        $db->raw("DROP TABLE IF EXISTS users;");
    }
};
