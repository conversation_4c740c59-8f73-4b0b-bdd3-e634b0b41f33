<?php

namespace App\Models;

use App\Core\Database;
use App\Core\Model;

/**
 * Branch Model
 * 
 * Handles branch-related database operations including branch retrieval,
 * configuration management, and branch-specific settings.
 */
class Branch extends Model
{
    /** @var Database */
    private $db;

    /** @var string */
    protected static $table = 'branches';

    /** @var array */
    protected static $fillable = [
        'code', 'description'
    ];

    /**
     * Branch constructor
     * 
     * Initializes the database connection for branch operations.
     */
    public function __construct()
    {
        $this->db = DB();
    }

    /**
     * Get all active branches
     *
     * Retrieves all active branch records from the branches table.
     *
     * @return array Array of active branch records
     */
    public function getAllActiveBranches()
    {
        $records = static::where('active', 1)->orderBy('code', 'ASC')->get();
        return static::newCollection($records);
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the users that have access to this branch
     *
     * @return \App\Core\Relationship
     */
    public static function users()
    {
        return static::belongsToMany(User::class, 'user_branch', 'branch_id', 'user_id');
    }

    /**
     * Get the user privileges for this branch
     *
     * @return \App\Core\Relationship
     */
    public static function userPrivileges()
    {
        return static::hasMany(UserPrivilege::class, 'branch_id', 'id');
    }

    /**
     * Get branch by code
     * 
     * Retrieves a specific branch by its unique code.
     * 
     * @param string $branchCode The branch code (e.g., 'HQ', 'GC')
     * @return array|false Branch data or false if not found
     * @throws \Exception If database operation fails
     */
    public function getBranchByCode($branchCode)
    {
        return $this->db->table('branches')
            ->where('code', $branchCode)
            ->where('active', 1)
            ->first();
    }

    /**
     * Create new branch
     * 
     * Creates a new branch with the provided data.
     * 
     * @param array $data branch data (code, description, etc.)
     * @return bool True if creation successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public function createBranch(array $data)
    {
        $requiredFields = ['code', 'description'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new \Exception("Required field missing: {$field}");
            }
        }

        $data['updated_at'] = date('Y-m-d H:i:s');
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['active'] = isset($data['active']) ? $data['active'] : 1;

        return $this->db->table('branches')->insert($data);
    }
}
