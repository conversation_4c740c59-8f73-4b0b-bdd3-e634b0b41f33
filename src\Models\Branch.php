<?php

namespace App\Models;

use App\Core\Database;

/**
 * Branch Model
 * 
 * Handles branch-related database operations including branch retrieval,
 * configuration management, and branch-specific settings.
 */
class Branch
{
    /** @var Database */
    private $db;

    /**
     * Branch constructor
     * 
     * Initializes the database connection for branch operations.
     */
    public function __construct()
    {
        $this->db = DB();
    }

    /**
     * Get all active branches
     * 
     * Retrieves all active branch records from the branches table.
     * 
     * @return array Array of active branch records
     */
    public function getAllActiveBranches()
    {
        return $this->db->table('branches')
            ->where('active', 1)
            ->orderBy('code', 'ASC')
            ->get();
    }

    /**
     * Get branch by ID
     * 
     * Retrieves a specific branch by its ID.
     * 
     * @param int $branchId The branch ID
     * @return array|false Branch data or false if not found
     * @throws \Exception If database operation fails
     */
    public function getBranchById($branchId)
    {
        return $this->db->table('branches')
            ->where('id', $branchId)
            ->where('active', 1)
            ->first();
    }

    /**
     * Get branch by code
     * 
     * Retrieves a specific branch by its unique code.
     * 
     * @param string $branchCode The branch code (e.g., 'HQ', 'GC')
     * @return array|false Branch data or false if not found
     * @throws \Exception If database operation fails
     */
    public function getBranchByCode($branchCode)
    {
        return $this->db->table('branches')
            ->where('code', $branchCode)
            ->where('active', 1)
            ->first();
    }

    /**
     * Create new branch
     * 
     * Creates a new branch with the provided data.
     * 
     * @param array $data branch data (code, description, etc.)
     * @return bool True if creation successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public function createBranch(array $data)
    {
        $requiredFields = ['code', 'description'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new \Exception("Required field missing: {$field}");
            }
        }

        $data['updated_at'] = date('Y-m-d H:i:s');
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['active'] = isset($data['active']) ? $data['active'] : 1;

        return $this->db->table('branches')->insert($data);
    }
}
