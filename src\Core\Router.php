<?php

namespace App\Core;

use App\Exceptions\NotFoundException;
use App\Exceptions\HttpException;
use App\Services\ErrorHandlerService;
use App\Services\DebugBarService;

class Router
{
    /** @var string */
    protected static $cache_file = __DIR__ . '/../../bootstrap/data/route.php';
    /** @var array */
    protected static $routes = [];
    /** @var array */
    protected static $groupOptions = [];
    /** @var array */
    protected static $globalMiddleware = [];

    public static function getRoute($name)
    {
        foreach (self::$routes as $val) {
            if ($val['name'] === $name) {
                return "/" . $val['uri'];
            }
        }

        throw new \Exception("Route [$name] not found.");
    }

    public static function getRoutes()
    {
        return self::$routes;
    }

    /**
     * Register global middleware that runs on every request
     * @param array $middleware
     */
    public static function globalMiddleware(array $middleware)
    {
        self::$globalMiddleware = array_merge(self::$globalMiddleware, $middleware);
    }

    /**
     * Get all registered global middleware
     * @return array
     */
    public static function getGlobalMiddleware()
    {
        return self::$globalMiddleware;
    }

    public static function cache()
    {
        self::cacheClear();

        static::loadRoute();
                
        file_put_contents(self::$cache_file,
            "<?php\nreturn "
            .var_export(self::$routes, true)
            .";\n"
        );
    }

    public static function cacheClear()
    {
        if (file_exists(self::$cache_file)) {
            unlink(self::$cache_file);
        }
    }

    public static function loadRoute()
    {
        if (file_exists(self::$cache_file)) {
            self::$routes = require_once self::$cache_file;
        } else {
            // Load routes
            require_once __DIR__ . '/../../routes/web.php';

            self::group(['prefix' => 'api'], function () {
                require_once __DIR__ . '/../../routes/api.php';
            });
        }
    }

    /**
     * @param string $uri
     * @param string $action
     * @param array $options
     */
    public static function get($uri, $action, array $options = [])
    {
        self::addRoute('GET', $uri, $action, $options);
    }

    /**
     * @param string $uri
     * @param string $action
     * @param array $options
     */
    public static function post($uri, $action, array $options = [])
    {
        self::addRoute('POST', $uri, $action, $options);
    }

    /**
     * @param array $options
     * @param callable $callback
     */
    public static function group(array $options, callable $callback)
    {
        // Save current group options
        $parentGroup = self::$groupOptions;

        if (isset($parentGroup['prefix']) && $parentGroup['prefix'] && isset($options['prefix']) && $options['prefix']) {
            $options['prefix'] = $parentGroup['prefix'] . "/" . $options['prefix'];
        }

        if (isset($parentGroup['middleware']) && $parentGroup['middleware'] && isset($options['middleware']) && $options['middleware']) {
            if (!is_array($parentGroup['middleware'])) $parentGroup['middleware'] = [$parentGroup['middleware']];
            if (!is_array($options['middleware'])) $options['middleware'] = [$options['middleware']];
            $options['middleware'] = array_merge($parentGroup['middleware'], $options['middleware']);
            $options['middleware'] = array_values(array_unique($options['middleware']));
        }

        if (isset($parentGroup['namespace']) && $parentGroup['namespace'] && isset($options['namespace']) && $options['namespace']) {
            $options['namespace'] = $parentGroup['namespace'] . "\\" . $options['namespace'];
        }

        self::$groupOptions = array_merge(self::$groupOptions, $options);

        $callback(); // Execute group routes

        // Reset group options after
        self::$groupOptions = $parentGroup;
    }

    /**
     * @param string $method
     * @param string $uri
     * @param string $action
     * @param array $options
     */
    protected static function addRoute($method, $uri, $action, array $options = [])
    {
        $prefix = isset(self::$groupOptions['prefix']) ? self::$groupOptions['prefix'] : '';
        $middleware = isset(self::$groupOptions['middleware']) ? self::$groupOptions['middleware'] : null;
        $namespace = isset(self::$groupOptions['namespace']) ? self::$groupOptions['namespace'] : null;
        $uri = trim($prefix . '/' . trim($uri, '/'), '/');
        $name = isset($options['name']) ? $options['name'] : null;

        if ($middleware && !is_array($middleware )) $middleware = [ $middleware ];

        // if ($name && isset(self::$routes[$name])) {
        //     throw new \Exception("Route name '$name' already exists.");
        // }

        self::$routes[] = [
            'method'     => $method,
            'uri'        => $uri,
            'action'     => $action,
            'middleware' => $middleware,
            'namespace'  => $namespace,
            'name'       => $name,
        ];
    }

    /**
     * @param string $requestUri
     * @param string $requestMethod
     */
    public static function dispatch($requestUri, $requestMethod)
    {
        static::loadRoute();

        // Run global middleware first
        foreach (self::$globalMiddleware as $middleware) {
            if (!self::runMiddleware($middleware)) {
                return;
            }
        }

        $uri = trim(parse_url($requestUri, PHP_URL_PATH), '/');

        foreach (self::$routes as $route) {
            // Special handling for debug bar assets - allow any characters in file parameter
            if (strpos($route['uri'], 'vendor/php-debugbar') !== false) { 
                $pattern = preg_replace('#\{file\}#', '(.+)', $route['uri']);
            } else {
                $pattern = preg_replace('#\{([\w]+)\}#', '([\w-]+)', $route['uri']);
            }
            $pattern = "#^{$pattern}$#";

            if (
                $route['method'] === $requestMethod &&
                preg_match($pattern, $uri, $matches)
            ) {
                array_shift($matches);

                $routeMiddleware = isset($route['middleware']) ? $route['middleware'] : [];
                foreach ($routeMiddleware as $middleware) {

                    logger("Middleware: {$middleware}", 'info');

                    // 🔐 Middleware
                    if (!self::runMiddleware($middleware)) {
                        return;
                    }
                }

                $action = $route['action'];
                if ($route['namespace']) $action = $route['namespace'] . "\\" . $route['action'];

                return self::callAction($action, $matches);
            }
        }

        // No route found - throw 404 exception
        throw new NotFoundException('The requested page could not be found.');
    }

    /**
     * @param string $middleware
     * @return bool
     */
    protected static function runMiddleware($middleware)
    {
        if (!$middleware) {
            return true;
        }

        $middleware = explode(':', $middleware);
        $params =  $middleware[1] ?? null;
        $middleware = $middleware[0];
        
        $middlewareClass = 'App\\Middleware\\' . ucfirst($middleware) . 'Middleware';

        if (!class_exists($middlewareClass)) {
            throw new HttpException(500, "Middleware $middlewareClass not found.");
        }

        $instance = new $middlewareClass();

        if (!method_exists($instance, 'handle')) {
            throw new HttpException(500, "Middleware must have a handle() method.");
        }

        return $instance->handle($params);
    }

    /**
     * @param string $action
     * @param array $params
     */
    protected static function callAction($action, array $params)
    {
        $actionParts = explode('@', $action);
        $controllerName = $actionParts[0];
        $method = $actionParts[1];
        $controllerClass = "App\\Controllers\\$controllerName";

        if (!class_exists($controllerClass)) {
            throw new NotFoundException("Controller $controllerName not found.");
        }

        $controller = new $controllerClass();

        if (!method_exists($controller, $method)) {
            throw new NotFoundException("Method $method not found in controller $controllerName.");
        }

        call_user_func_array([$controller, $method], $params);
    }
}
