# DD-Like Debugging Functions

This document explains the Laravel-style debugging functions added to the helper functions.

## Overview

The application now includes powerful debugging helpers inspired by <PERSON><PERSON>'s `dd()` function:
- `dd()` - Dump and die with beautiful formatting
- `dump()` - Dump without stopping execution (Note: may conflict with <PERSON><PERSON>fony's dump)
- `ddd()` - Enhanced dump with system information
- `ray()` - Ray-style debugging with chaining support
- `formatBytes()` - Human-readable byte formatting

## Functions

### 1. dd(...$vars): void

**Dump and Die** - The main debugging function that displays variables in a beautiful format and stops execution.

**Features:**
- Beautiful dark theme with syntax highlighting
- Color-coded variable types
- Proper indentation for nested structures
- Shows file and line number where called
- Includes stack trace
- Supports multiple variables
- Handles arrays, objects, primitives, and complex structures

**Example:**
```php
$user = ['name' => 'John', 'age' => 30];
$active = true;

dd($user, $active); // Dumps both variables and stops execution
```

**Output Features:**
- **Strings**: Shows length and content with escaping
- **Arrays**: Displays structure with proper indentation
- **Objects**: Shows class name and all properties (public, protected, private)
- **Primitives**: Color-coded booleans, numbers, null values
- **Recursion Protection**: Prevents infinite loops with max depth

### 2. dump(...$vars): void

**Dump Without Dying** - Similar to `dd()` but doesn't stop execution.

**Note:** This function may conflict with Symfony's VarDumper if installed. Consider using `dd()` or `ray()` instead.

**Example:**
```php
$data = ['debug' => 'info'];
dump($data); // Shows the data
echo "Execution continues..."; // This will run
```

### 3. ddd(...$vars): void

**Enhanced Dump and Die** - Like `dd()` but includes additional system information.

**Additional Features:**
- Memory usage (current and peak)
- Execution time since request start
- Request method and URI
- User agent information
- Extended stack trace (10 levels)

**Example:**
```php
$complexData = ['users' => $users, 'config' => $config];
ddd($complexData); // Enhanced debug with system info
```

**System Information Displayed:**
- Memory Usage: Current and peak memory consumption
- Execution Time: Time elapsed since request start
- Request Details: Method, URI, User Agent
- Extended Stack Trace: More detailed call stack

### 4. ray(...$vars): mixed

**Ray-Style Debugging** - Inspired by Spatie's Ray, logs to debug bar and returns first variable for chaining.

**Features:**
- Non-blocking (doesn't stop execution)
- Logs to PHP Debug Bar
- Returns first variable for method chaining
- Provides variable type and preview in logs

**Example:**
```php
// Basic usage
ray($user, $settings);

// Method chaining
$name = ray($user)['name']; // Logs $user and returns it for chaining

// Chaining with objects
$result = ray($api->getUsers())->filter(fn($u) => $u->active);
```

### 5. formatBytes(int $bytes, int $precision = 2): string

**Human-Readable Byte Formatting** - Converts bytes to readable format.

**Example:**
```php
echo formatBytes(1024);        // "1.00 KB"
echo formatBytes(1048576);     // "1.00 MB"
echo formatBytes(1073741824);  // "1.00 GB"

// Memory usage
echo "Memory: " . formatBytes(memory_get_usage(true));
```

## Styling and Appearance

### Visual Design
- **Dark Theme**: Professional dark background (#1e1e1e)
- **Syntax Highlighting**: Color-coded elements
- **Typography**: Monospace font (Fira Code, Monaco, Consolas)
- **Responsive**: Works on different screen sizes

### Color Scheme
- **Types**: Teal (#4ec9b0) for data types
- **Strings**: Orange (#ce9178) for string values
- **Numbers**: Light green (#b5cea8) for numeric values
- **Booleans**: Blue (#569cd6) for true/false
- **Keys**: Light blue (#9cdcfe) for array/object keys
- **Null**: Gray (#808080) for null values

### Layout Elements
- **Headers**: Clear section headers with file/line info
- **Indentation**: Proper nesting visualization
- **Separators**: Clean dividers between multiple variables
- **Stack Trace**: Formatted call stack information

## Integration with Debug Bar

All debugging functions integrate seamlessly with the PHP Debug Bar:

```php
// These calls will appear in the debug bar messages
ray($data);
dd($user); // Also logs before stopping execution
ddd($config); // Logs enhanced debug info
```

**Debug Bar Benefits:**
- Persistent logging across requests
- Searchable message history
- Performance impact monitoring
- Request correlation

## Usage Examples

### Basic Debugging
```php
// Quick variable inspection
dd($user);

// Multiple variables
dd($user, $settings, $permissions);

// Complex data structures
dd([
    'request' => $_REQUEST,
    'session' => $_SESSION,
    'config' => config('app')
]);
```

### Development Workflow
```php
// Debug API responses
$response = $api->getUsers();
ray($response); // Log to debug bar
return $response->data;

// Debug database queries
$users = DB()->table('users')->where('active', 1)->get();
ddd($users); // Enhanced debug with memory info

// Debug form processing
ray($_POST, $_FILES); // Log form data
$result = processForm($_POST);
dd($result); // Final result inspection
```

### Performance Debugging
```php
// Memory usage tracking
ray("Memory before: " . formatBytes(memory_get_usage()));
$data = processLargeDataset();
ddd("Memory after processing", formatBytes(memory_get_usage()));

// Execution time monitoring
$start = microtime(true);
$result = expensiveOperation();
$time = (microtime(true) - $start) * 1000;
ray("Operation took: {$time}ms");
```

## Best Practices

### 1. Use Appropriate Function
- **dd()**: When you need to stop and inspect
- **ray()**: For non-blocking debugging and chaining
- **ddd()**: When you need system information
- **formatBytes()**: For memory/file size display

### 2. Remove Before Production
```php
// Good: Conditional debugging
if (config('app.debug')) {
    dd($sensitiveData);
}

// Better: Use ray() for less intrusive debugging
ray($data); // Can be left in code, only logs when debug bar is enabled
```

### 3. Meaningful Context
```php
// Good: Provide context
ray("User data after validation", $user);
ray("Database query result", $result);

// Better: Use descriptive messages
ray("API Response from /users endpoint", $apiResponse);
```

## Browser Testing

To test the functions in a browser:

1. **Access Test Page**: Visit `/test_dd.php` in your browser
2. **Choose Test Type**: Select different test scenarios
3. **Inspect Output**: See the beautiful formatted output
4. **Check Debug Bar**: View logged messages in debug bar

**Test URLs:**
- `/test_dd.php?test=dd` - Test basic dd() function
- `/test_dd.php?test=ddd` - Test enhanced ddd() function
- `/test_dd.php?test=ray` - Test ray() chaining
- `/test_dd.php?test=multiple` - Test multiple variables

## Troubleshooting

### Issue: dump() conflicts with Symfony VarDumper
**Solution:** Use `dd()` or `ray()` instead, or check function existence:
```php
if (function_exists('dump') && !class_exists('Symfony\Component\VarDumper\VarDumper')) {
    dump($data);
} else {
    dd($data);
}
```

### Issue: Styling not appearing
**Solution:** Ensure headers aren't sent before calling dd():
```php
// Wrong
echo "Some output";
dd($data); // Headers already sent

// Right
dd($data); // Call before any output
```

### Issue: Functions not available
**Solution:** Ensure helper.php is loaded:
```php
require_once 'src/helper.php';
```

## Security Considerations

### Production Safety
- Functions check for debug mode when possible
- Sensitive data should be filtered before dumping
- Consider using ray() for less intrusive debugging

### Data Sanitization
```php
// Good: Filter sensitive data
$safeUser = array_diff_key($user, ['password', 'token']);
dd($safeUser);

// Better: Use ray() with context
ray("User data (sanitized)", $safeUser);
```

## Compatibility

- **PHP Version**: 7.4+
- **Dependencies**: None (standalone functions)
- **Debug Bar**: Optional integration
- **Browsers**: All modern browsers for styling
- **CLI**: Basic output without styling

## Performance Impact

- **dd()/ddd()**: Minimal impact (stops execution)
- **ray()**: Very low impact (just logging)
- **formatBytes()**: Negligible impact
- **Styling**: Only applied when functions are called

The debugging functions are designed to have minimal performance impact and can be safely used during development.
