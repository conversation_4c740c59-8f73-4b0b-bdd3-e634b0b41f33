<?php

namespace App\Exceptions;

/**
 * 405 Method Not Allowed Exception
 * 
 * Thrown when the HTTP method is not allowed for the requested resource.
 */
class MethodNotAllowedException extends HttpException
{
    public function __construct(
        array $allowedMethods = [],
        string $message = 'The request method is not allowed for this resource.',
        array $errorData = [],
        array $headers = []
    ) {
        // Add Allow header if allowed methods are provided
        if (!empty($allowedMethods)) {
            $headers['Allow'] = implode(', ', $allowedMethods);
            $errorData['allowed_methods'] = $allowedMethods;
        }

        parent::__construct(405, $message, $errorData, $headers);
    }
}
