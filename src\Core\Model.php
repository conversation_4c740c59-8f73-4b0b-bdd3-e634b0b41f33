<?php

namespace App\Core;

/**
 * Base Model Class
 *
 * Provides Laravel-like Eloquent functionality for all models.
 * Models extending this class get static methods like find(), create(), etc.
 */
abstract class Model
{
    /** @var string */
    protected static $table;

    /** @var string */
    protected static $primaryKey = 'id';

    /** @var array */
    protected static $fillable = [];

    /** @var array */
    protected static $guarded = ['id'];

    /** @var bool */
    protected static $timestamps = true;

    /**
     * Get the table name for the model
     *
     * @return string
     */
    protected static function getTable()
    {
        if (static::$table) {
            return static::$table;
        }

        // Auto-generate table name from class name (e.g., User -> users)
        $className = class_basename(static::class);
        return strtolower($className) . 's';
    }

    /**
     * Get a new database query builder instance
     *
     * @return \App\Core\Database
     */
    protected static function query()
    {
        return DB()->table(static::getTable());
    }

    // ===== ELOQUENT-LIKE STATIC METHODS =====

    /**
     * Find a record by primary key
     *
     * @param mixed $id The primary key value
     * @return array|null Record data or null if not found
     * @throws \Exception If database operation fails
     */
    public static function find($id)
    {
        return static::query()
            ->where(static::$primaryKey, $id)
            ->first();
    }

    /**
     * Find a record by primary key or throw exception
     *
     * @param mixed $id The primary key value
     * @return array Record data
     * @throws \Exception If record not found or database operation fails
     */
    public static function findOrFail($id)
    {
        $record = static::find($id);
        
        if (!$record) {
            $modelName = class_basename(static::class);
            throw new \Exception("{$modelName} with ID {$id} not found");
        }
        
        return $record;
    }

    /**
     * Find multiple records by primary keys
     *
     * @param array $ids Array of primary key values
     * @return array Array of records
     * @throws \Exception If database operation fails
     */
    public static function findMany(array $ids)
    {
        return static::query()
            ->whereIn(static::$primaryKey, $ids)
            ->get();
    }

    /**
     * Get all records
     *
     * @return array Array of all records
     * @throws \Exception If database operation fails
     */
    public static function all()
    {
        return static::query()->get();
    }

    /**
     * Start a where query
     *
     * @param string $column Column name
     * @param mixed $operator Operator or value if using '='
     * @param mixed $value Value (optional if operator is the value)
     * @return \App\Core\Database Query builder instance
     * @throws \Exception If database operation fails
     */
    public static function where($column, $operator = null, $value = null)
    {
        if ($value === null) {
            return static::query()->where($column, $operator);
        }
        
        return static::query()->where($column, $operator, $value);
    }

    /**
     * Find first record matching where clause
     *
     * @param string $column Column name
     * @param mixed $operator Operator or value if using '='
     * @param mixed $value Value (optional if operator is the value)
     * @return array|null Record data or null if not found
     * @throws \Exception If database operation fails
     */
    public static function whereFirst($column, $operator = null, $value = null)
    {
        if ($value === null) {
            return static::query()->where($column, $operator)->first();
        }
        
        return static::query()->where($column, $operator, $value)->first();
    }

    /**
     * Create a new record
     *
     * @param array $data Record data
     * @return bool True if creation successful
     * @throws \Exception If database operation fails
     */
    public static function create(array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Remove guarded fields
        if (!empty(static::$guarded)) {
            $data = array_diff_key($data, array_flip(static::$guarded));
        }

        // Add timestamps if enabled
        if (static::$timestamps) {
            if (!isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
            if (!isset($data['updated_at'])) {
                $data['updated_at'] = date('Y-m-d H:i:s');
            }
        }

        return static::query()->insert($data);
    }

    /**
     * Update record by primary key
     *
     * @param mixed $id Primary key value
     * @param array $data Data to update
     * @return bool True if update successful
     * @throws \Exception If database operation fails
     */
    public static function updateById($id, array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Remove guarded fields
        if (!empty(static::$guarded)) {
            $data = array_diff_key($data, array_flip(static::$guarded));
        }

        // Add updated timestamp if enabled
        if (static::$timestamps) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }

        return static::query()
            ->where(static::$primaryKey, $id)
            ->update($data);
    }

    /**
     * Delete record by primary key
     *
     * @param mixed $id Primary key value
     * @return bool True if deletion successful
     * @throws \Exception If database operation fails
     */
    public static function destroy($id)
    {
        return static::query()
            ->where(static::$primaryKey, $id)
            ->delete();
    }

    /**
     * Count total records
     *
     * @return int Number of records
     * @throws \Exception If database operation fails
     */
    public static function count()
    {
        return static::query()->count();
    }

    /**
     * Get first record
     *
     * @return array|null First record or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function first()
    {
        return static::query()->first();
    }

    /**
     * Get latest record (by primary key)
     *
     * @return array|null Latest record or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function latest()
    {
        return static::query()
            ->orderBy(static::$primaryKey, 'DESC')
            ->first();
    }

    /**
     * Get oldest record (by primary key)
     *
     * @return array|null Oldest record or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function oldest()
    {
        return static::query()
            ->orderBy(static::$primaryKey, 'ASC')
            ->first();
    }

    /**
     * Create multiple records
     *
     * @param array $records Array of record data arrays
     * @return bool True if all records were created successfully
     * @throws \Exception If database operation fails
     */
    public static function createMany(array $records)
    {
        if (empty($records)) {
            throw new \Exception("Records array cannot be empty");
        }

        // Process each record
        $processedRecords = [];
        foreach ($records as $data) {
            // Filter fillable fields if defined
            if (!empty(static::$fillable)) {
                $data = array_intersect_key($data, array_flip(static::$fillable));
            }

            // Remove guarded fields
            if (!empty(static::$guarded)) {
                $data = array_diff_key($data, array_flip(static::$guarded));
            }

            // Add timestamps if enabled
            if (static::$timestamps) {
                if (!isset($data['created_at'])) {
                    $data['created_at'] = date('Y-m-d H:i:s');
                }
                if (!isset($data['updated_at'])) {
                    $data['updated_at'] = date('Y-m-d H:i:s');
                }
            }

            $processedRecords[] = $data;
        }

        return static::query()->insertMany($processedRecords);
    }

    /**
     * Upsert (insert or update) a record
     *
     * @param array $data Record data
     * @return bool True if operation successful
     * @throws \Exception If database operation fails
     */
    public static function upsert(array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Add timestamps if enabled
        if (static::$timestamps) {
            if (!isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
            $data['updated_at'] = date('Y-m-d H:i:s');
        }

        return static::query()->replace($data);
    }
}
