<?php

namespace App\Core;

/**
 * Base Model Class
 *
 * Provides Laravel-like Eloquent functionality for all models.
 * Models extending this class get static methods like find(), create(), etc.
 * Returns ModelInstance objects instead of arrays for better OOP experience.
 */
abstract class Model
{
    /** @var string */
    protected static $table;

    /** @var string|array */
    protected static $primaryKey = 'id';

    /** @var array */
    protected static $fillable = [];

    /** @var array */
    protected static $guarded = ['id'];

    /** @var bool */
    protected static $timestamps = true;

    /**
     * Get the table name for the model
     *
     * @return string
     */
    protected static function getTable()
    {
        if (static::$table) {
            return static::$table;
        }

        // Auto-generate table name from class name (e.g., User -> users)
        $className = class_basename(static::class);
        return strtolower($className) . 's';
    }

    /**
     * Check if the model uses a composite primary key
     *
     * @return bool
     */
    protected static function hasCompositePrimaryKey(): bool
    {
        return is_array(static::$primaryKey);
    }

    /**
     * Get the primary key columns as array
     *
     * @return array
     */
    protected static function getPrimaryKeyColumns(): array
    {
        return static::hasCompositePrimaryKey() ? static::$primaryKey : [static::$primaryKey];
    }

    /**
     * Build where conditions for primary key
     *
     * @param mixed $id Primary key value(s)
     * @return array Where conditions
     * @throws \Exception If composite key values don't match columns
     */
    protected static function buildPrimaryKeyWhere($id): array
    {
        $columns = static::getPrimaryKeyColumns();

        if (static::hasCompositePrimaryKey()) {
            if (!is_array($id)) {
                throw new \Exception("Composite primary key requires array of values");
            }

            if (count($id) !== count($columns)) {
                throw new \Exception("Number of key values must match number of primary key columns");
            }
            
            $where = [];
            foreach ($columns as $index => $column) {
                $where[$column] = $id[$index];
            }
            return $where;
        } else {
            return [$columns[0] => $id];
        }
    }

    /**
     * Get a new database query builder instance
     *
     * @return \App\Core\Database
     */
    protected static function query()
    {
        return DB()->table(static::getTable());
    }

    /**
     * Create a new model instance from array data
     *
     * @param array|null $data Record data
     * @param bool $exists Whether the record exists in database
     * @return ModelInstance|null Model instance or null if no data
     */
    protected static function newInstance(?array $data, bool $exists = true): ?ModelInstance
    {
        if ($data === null) {
            return null;
        }

        return new ModelInstance(
            $data,
            static::class,
            static::getTable(),
            static::$primaryKey,
            $exists
        );
    }

    /**
     * Create multiple model instances from array data
     *
     * @param array $records Array of record data
     * @param bool $exists Whether the records exist in database
     * @return array Array of ModelInstance objects
     */
    public static function newCollection(array $records, bool $exists = true): array
    {
        $instances = [];
        foreach ($records as $record) {
            $instances[] = static::newInstance($record, $exists);
        }
        return $instances;
    }

    // ===== ELOQUENT-LIKE STATIC METHODS =====

    /**
     * Find a record by primary key
     *
     * @param mixed $id The primary key value (single value or array for composite keys)
     * @return ModelInstance|null Model instance or null if not found
     * @throws \Exception If database operation fails
     */
    public static function find($id)
    {
        $query = static::query();
        $whereConditions = static::buildPrimaryKeyWhere($id);
        
        foreach ($whereConditions as $column => $value) {
            $query->where($column, $value);
        }

        $data = $query->first();
        return static::newInstance($data);
    }

    /**
     * Find a record by primary key or throw exception
     *
     * @param mixed $id The primary key value
     * @return ModelInstance Model instance
     * @throws \Exception If record not found or database operation fails
     */
    public static function findOrFail($id)
    {
        $record = static::find($id);

        if (!$record) {
            $modelName = class_basename(static::class);
            throw new \Exception("{$modelName} with ID {$id} not found");
        }

        return $record;
    }

    /**
     * Find multiple records by primary keys
     *
     * @param array $ids Array of primary key values (for composite keys, array of arrays)
     * @return array Array of ModelInstance objects
     * @throws \Exception If database operation fails
     */
    public static function findMany(array $ids)
    {
        if (empty($ids)) {
            return [];
        }

        if (static::hasCompositePrimaryKey()) {
            // For composite keys, use OR conditions for each set of key values
            $query = static::query();
            $first = true;

            foreach ($ids as $compositeId) {
                $whereConditions = static::buildPrimaryKeyWhere($compositeId);

                if ($first) {
                    foreach ($whereConditions as $column => $value) {
                        $query->where($column, $value);
                    }
                    $first = false;
                } else {
                    $query->orWhere(function($subQuery) use ($whereConditions) {
                        foreach ($whereConditions as $column => $value) {
                            $subQuery->where($column, $value);
                        }
                    });
                }
            }

            $records = $query->get();
        } else {
            // For single primary key, use whereIn
            $records = static::query()
                ->whereIn(static::$primaryKey, $ids)
                ->get();
        }

        return static::newCollection($records);
    }

    /**
     * Get all records
     *
     * @return array Array of ModelInstance objects
     * @throws \Exception If database operation fails
     */
    public static function all()
    {
        $records = static::query()->get();
        return static::newCollection($records);
    }

    /**
     * Start a where query
     *
     * @param string $column Column name
     * @param mixed $operator Operator or value if using '='
     * @param mixed $value Value (optional if operator is the value)
     * @return \App\Core\Database Query builder instance
     * @throws \Exception If database operation fails
     */
    public static function where($column, $operator = null, $value = null)
    {
        if ($value === null) {
            return static::query()->where($column, $operator);
        }
        
        return static::query()->where($column, $operator, $value);
    }

    /**
     * Find first record matching where clause
     *
     * @param string $column Column name
     * @param mixed $operator Operator or value if using '='
     * @param mixed $value Value (optional if operator is the value)
     * @return ModelInstance|null Model instance or null if not found
     * @throws \Exception If database operation fails
     */
    public static function whereFirst($column, $operator = null, $value = null)
    {
        if ($value === null) {
            $data = static::query()->where($column, $operator)->first();
        } else {
            $data = static::query()->where($column, $operator, $value)->first();
        }

        return static::newInstance($data);
    }

    /**
     * Get records from a where query
     *
     * @param \App\Core\Database $query The query builder instance
     * @return array Array of ModelInstance objects
     * @throws \Exception If database operation fails
     */
    protected static function getFromQuery($query)
    {
        $records = $query->get();
        return static::newCollection($records);
    }

    /**
     * Create a new record
     *
     * @param array $data Record data
     * @return ModelInstance|bool Model instance if creation successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public static function create(array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Remove guarded fields
        if (!empty(static::$guarded)) {
            $data = array_diff_key($data, array_flip(static::$guarded));
        }

        // Add timestamps if enabled
        if (static::$timestamps) {
            if (!isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
            if (!isset($data['updated_at'])) {
                $data['updated_at'] = date('Y-m-d H:i:s');
            }
        }

        $result = static::query()->insert($data);

        if ($result) {
            // Get the last inserted ID
            $lastId = DB()->lastInsertId();

            if ($lastId) {
                // Create a new instance with the inserted data
                $data[static::$primaryKey] = $lastId;
                return static::newInstance($data, true);
            }
        }

        return $result;
    }

    /**
     * Update record by primary key
     *
     * @param mixed $id Primary key value (single value or array for composite keys)
     * @param array $data Data to update
     * @return bool True if update successful
     * @throws \Exception If database operation fails
     */
    public static function updateById($id, array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Remove guarded fields
        if (!empty(static::$guarded)) {
            $data = array_diff_key($data, array_flip(static::$guarded));
        }

        // Add updated timestamp if enabled
        if (static::$timestamps) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }

        $query = static::query();
        $whereConditions = static::buildPrimaryKeyWhere($id);

        foreach ($whereConditions as $column => $value) {
            $query->where($column, $value);
        }

        return $query->update($data);
    }

    /**
     * Delete record by primary key
     *
     * @param mixed $id Primary key value (single value or array for composite keys)
     * @return bool True if deletion successful
     * @throws \Exception If database operation fails
     */
    public static function destroy($id)
    {
        $query = static::query();
        $whereConditions = static::buildPrimaryKeyWhere($id);

        foreach ($whereConditions as $column => $value) {
            $query->where($column, $value);
        }

        return $query->delete();
    }

    /**
     * Count total records
     *
     * @return int Number of records
     * @throws \Exception If database operation fails
     */
    public static function count()
    {
        return static::query()->count();
    }

    /**
     * Get first record
     *
     * @return ModelInstance|null First model instance or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function first()
    {
        $data = static::query()->first();
        return static::newInstance($data);
    }

    /**
     * Get latest record (by primary key)
     *
     * @return ModelInstance|null Latest model instance or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function latest()
    {
        $data = static::query()
            ->orderBy(static::$primaryKey, 'DESC')
            ->first();

        return static::newInstance($data);
    }

    /**
     * Get oldest record (by primary key)
     *
     * @return ModelInstance|null Oldest model instance or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function oldest()
    {
        $data = static::query()
            ->orderBy(static::$primaryKey, 'ASC')
            ->first();

        return static::newInstance($data);
    }

    /**
     * Create multiple records
     *
     * @param array $records Array of record data arrays
     * @return bool True if all records were created successfully
     * @throws \Exception If database operation fails
     */
    public static function createMany(array $records)
    {
        if (empty($records)) {
            throw new \Exception("Records array cannot be empty");
        }

        // Process each record
        $processedRecords = [];
        foreach ($records as $data) {
            // Filter fillable fields if defined
            if (!empty(static::$fillable)) {
                $data = array_intersect_key($data, array_flip(static::$fillable));
            }

            // Remove guarded fields
            if (!empty(static::$guarded)) {
                $data = array_diff_key($data, array_flip(static::$guarded));
            }

            // Add timestamps if enabled
            if (static::$timestamps) {
                if (!isset($data['created_at'])) {
                    $data['created_at'] = date('Y-m-d H:i:s');
                }
                if (!isset($data['updated_at'])) {
                    $data['updated_at'] = date('Y-m-d H:i:s');
                }
            }

            $processedRecords[] = $data;
        }

        return static::query()->insertMany($processedRecords);
    }

    /**
     * Upsert (insert or update) a record
     *
     * @param array $data Record data
     * @return bool True if operation successful
     * @throws \Exception If database operation fails
     */
    public static function upsert(array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Add timestamps if enabled
        if (static::$timestamps) {
            if (!isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
            $data['updated_at'] = date('Y-m-d H:i:s');
        }

        return static::query()->replace($data);
    }

    // ===== RELATIONSHIP METHODS =====

    /**
     * Define a one-to-one relationship
     *
     * @param string $related The related model class
     * @param string|null $foreignKey The foreign key on the related table
     * @param string|null $localKey The local key on this table
     * @return Relationship
     */
    protected static function hasOne(string $related, ?string $foreignKey = null, ?string $localKey = null): Relationship
    {
        $foreignKey = $foreignKey ?: static::getForeignKey();
        $localKey = $localKey ?: static::$primaryKey;

        return new Relationship('hasOne', static::class, $related, $foreignKey, $localKey);
    }

    /**
     * Define a one-to-many relationship
     *
     * @param string $related The related model class
     * @param string|null $foreignKey The foreign key on the related table
     * @param string|null $localKey The local key on this table
     * @return Relationship
     */
    protected static function hasMany(string $related, ?string $foreignKey = null, ?string $localKey = null): Relationship
    {
        $foreignKey = $foreignKey ?: static::getForeignKey();
        $localKey = $localKey ?: static::$primaryKey;

        return new Relationship('hasMany', static::class, $related, $foreignKey, $localKey);
    }

    /**
     * Define a belongs-to relationship
     *
     * @param string $related The related model class
     * @param string|null $foreignKey The foreign key on this table
     * @param string|null $ownerKey The owner key on the related table
     * @return Relationship
     */
    protected static function belongsTo(string $related, ?string $foreignKey = null, ?string $ownerKey = null): Relationship
    {
        $foreignKey = $foreignKey ?: static::getRelatedForeignKey($related);
        $ownerKey = $ownerKey ?: (new $related)::$primaryKey;

        return new Relationship('belongsTo', static::class, $related, $foreignKey, $ownerKey);
    }

    /**
     * Define a many-to-many relationship
     *
     * @param string $related The related model class
     * @param string|null $table The pivot table name
     * @param string|null $foreignPivotKey The foreign key of this model in the pivot table
     * @param string|null $relatedPivotKey The foreign key of the related model in the pivot table
     * @return Relationship
     */
    protected static function belongsToMany(string $related, ?string $table = null, ?string $foreignPivotKey = null, ?string $relatedPivotKey = null): Relationship
    {
        $table = $table ?: static::getPivotTableName($related);
        $foreignPivotKey = $foreignPivotKey ?: static::getForeignKey();
        $relatedPivotKey = $relatedPivotKey ?: static::getRelatedForeignKey($related);

        return new Relationship('belongsToMany', static::class, $related, $foreignPivotKey, $relatedPivotKey, $table);
    }

    /**
     * Get the foreign key for this model
     *
     * @return string
     */
    protected static function getForeignKey(): string
    {
        $className = class_basename(static::class);
        return strtolower($className) . '_id';
    }

    /**
     * Get the foreign key for a related model
     *
     * @param string $related The related model class
     * @return string
     */
    protected static function getRelatedForeignKey(string $related): string
    {
        $className = class_basename($related);
        return strtolower($className) . '_id';
    }

    /**
     * Get the pivot table name for a many-to-many relationship
     *
     * @param string $related The related model class
     * @return string
     */
    protected static function getPivotTableName(string $related): string
    {
        $models = [
            strtolower(class_basename(static::class)),
            strtolower(class_basename($related))
        ];
        sort($models);
        return implode('_', $models);
    }
}
