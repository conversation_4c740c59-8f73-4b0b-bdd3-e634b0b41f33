<?php

namespace App\Core;

/**
 * Base Model Class
 *
 * Provides Laravel-like Eloquent functionality for all models.
 * Models extending this class get static methods like find(), create(), etc.
 * Returns ModelInstance objects instead of arrays for better OOP experience.
 */
abstract class Model
{
    /** @var string */
    protected static $table;

    /** @var string */
    protected static $primaryKey = 'id';

    /** @var array */
    protected static $fillable = [];

    /** @var array */
    protected static $guarded = ['id'];

    /** @var bool */
    protected static $timestamps = true;

    /**
     * Get the table name for the model
     *
     * @return string
     */
    protected static function getTable()
    {
        if (static::$table) {
            return static::$table;
        }

        // Auto-generate table name from class name (e.g., User -> users)
        $className = class_basename(static::class);
        return strtolower($className) . 's';
    }

    /**
     * Get a new database query builder instance
     *
     * @return \App\Core\Database
     */
    protected static function query()
    {
        return DB()->table(static::getTable());
    }

    /**
     * Create a new model instance from array data
     *
     * @param array|null $data Record data
     * @param bool $exists Whether the record exists in database
     * @return ModelInstance|null Model instance or null if no data
     */
    protected static function newInstance(?array $data, bool $exists = true): ?ModelInstance
    {
        if ($data === null) {
            return null;
        }

        return new ModelInstance(
            $data,
            static::class,
            static::getTable(),
            static::$primaryKey,
            $exists
        );
    }

    /**
     * Create multiple model instances from array data
     *
     * @param array $records Array of record data
     * @param bool $exists Whether the records exist in database
     * @return array Array of ModelInstance objects
     */
    protected static function newCollection(array $records, bool $exists = true): array
    {
        $instances = [];
        foreach ($records as $record) {
            $instances[] = static::newInstance($record, $exists);
        }
        return $instances;
    }

    // ===== ELOQUENT-LIKE STATIC METHODS =====

    /**
     * Find a record by primary key
     *
     * @param mixed $id The primary key value
     * @return ModelInstance|null Model instance or null if not found
     * @throws \Exception If database operation fails
     */
    public static function find($id)
    {
        $data = static::query()
            ->where(static::$primaryKey, $id)
            ->first();

        return static::newInstance($data);
    }

    /**
     * Find a record by primary key or throw exception
     *
     * @param mixed $id The primary key value
     * @return ModelInstance Model instance
     * @throws \Exception If record not found or database operation fails
     */
    public static function findOrFail($id)
    {
        $record = static::find($id);

        if (!$record) {
            $modelName = class_basename(static::class);
            throw new \Exception("{$modelName} with ID {$id} not found");
        }

        return $record;
    }

    /**
     * Find multiple records by primary keys
     *
     * @param array $ids Array of primary key values
     * @return array Array of ModelInstance objects
     * @throws \Exception If database operation fails
     */
    public static function findMany(array $ids)
    {
        $records = static::query()
            ->whereIn(static::$primaryKey, $ids)
            ->get();

        return static::newCollection($records);
    }

    /**
     * Get all records
     *
     * @return array Array of ModelInstance objects
     * @throws \Exception If database operation fails
     */
    public static function all()
    {
        $records = static::query()->get();
        return static::newCollection($records);
    }

    /**
     * Start a where query
     *
     * @param string $column Column name
     * @param mixed $operator Operator or value if using '='
     * @param mixed $value Value (optional if operator is the value)
     * @return \App\Core\Database Query builder instance
     * @throws \Exception If database operation fails
     */
    public static function where($column, $operator = null, $value = null)
    {
        if ($value === null) {
            return static::query()->where($column, $operator);
        }
        
        return static::query()->where($column, $operator, $value);
    }

    /**
     * Find first record matching where clause
     *
     * @param string $column Column name
     * @param mixed $operator Operator or value if using '='
     * @param mixed $value Value (optional if operator is the value)
     * @return ModelInstance|null Model instance or null if not found
     * @throws \Exception If database operation fails
     */
    public static function whereFirst($column, $operator = null, $value = null)
    {
        if ($value === null) {
            $data = static::query()->where($column, $operator)->first();
        } else {
            $data = static::query()->where($column, $operator, $value)->first();
        }

        return static::newInstance($data);
    }

    /**
     * Get records from a where query
     *
     * @param \App\Core\Database $query The query builder instance
     * @return array Array of ModelInstance objects
     * @throws \Exception If database operation fails
     */
    protected static function getFromQuery($query)
    {
        $records = $query->get();
        return static::newCollection($records);
    }

    /**
     * Create a new record
     *
     * @param array $data Record data
     * @return ModelInstance|bool Model instance if creation successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public static function create(array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Remove guarded fields
        if (!empty(static::$guarded)) {
            $data = array_diff_key($data, array_flip(static::$guarded));
        }

        // Add timestamps if enabled
        if (static::$timestamps) {
            if (!isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
            if (!isset($data['updated_at'])) {
                $data['updated_at'] = date('Y-m-d H:i:s');
            }
        }

        $result = static::query()->insert($data);

        if ($result) {
            // Get the last inserted ID
            $lastId = DB()->lastInsertId();

            if ($lastId) {
                // Create a new instance with the inserted data
                $data[static::$primaryKey] = $lastId;
                return static::newInstance($data, true);
            }
        }

        return $result;
    }

    /**
     * Update record by primary key
     *
     * @param mixed $id Primary key value
     * @param array $data Data to update
     * @return bool True if update successful
     * @throws \Exception If database operation fails
     */
    public static function updateById($id, array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Remove guarded fields
        if (!empty(static::$guarded)) {
            $data = array_diff_key($data, array_flip(static::$guarded));
        }

        // Add updated timestamp if enabled
        if (static::$timestamps) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }

        return static::query()
            ->where(static::$primaryKey, $id)
            ->update($data);
    }

    /**
     * Delete record by primary key
     *
     * @param mixed $id Primary key value
     * @return bool True if deletion successful
     * @throws \Exception If database operation fails
     */
    public static function destroy($id)
    {
        return static::query()
            ->where(static::$primaryKey, $id)
            ->delete();
    }

    /**
     * Count total records
     *
     * @return int Number of records
     * @throws \Exception If database operation fails
     */
    public static function count()
    {
        return static::query()->count();
    }

    /**
     * Get first record
     *
     * @return ModelInstance|null First model instance or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function first()
    {
        $data = static::query()->first();
        return static::newInstance($data);
    }

    /**
     * Get latest record (by primary key)
     *
     * @return ModelInstance|null Latest model instance or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function latest()
    {
        $data = static::query()
            ->orderBy(static::$primaryKey, 'DESC')
            ->first();

        return static::newInstance($data);
    }

    /**
     * Get oldest record (by primary key)
     *
     * @return ModelInstance|null Oldest model instance or null if no records exist
     * @throws \Exception If database operation fails
     */
    public static function oldest()
    {
        $data = static::query()
            ->orderBy(static::$primaryKey, 'ASC')
            ->first();

        return static::newInstance($data);
    }

    /**
     * Create multiple records
     *
     * @param array $records Array of record data arrays
     * @return bool True if all records were created successfully
     * @throws \Exception If database operation fails
     */
    public static function createMany(array $records)
    {
        if (empty($records)) {
            throw new \Exception("Records array cannot be empty");
        }

        // Process each record
        $processedRecords = [];
        foreach ($records as $data) {
            // Filter fillable fields if defined
            if (!empty(static::$fillable)) {
                $data = array_intersect_key($data, array_flip(static::$fillable));
            }

            // Remove guarded fields
            if (!empty(static::$guarded)) {
                $data = array_diff_key($data, array_flip(static::$guarded));
            }

            // Add timestamps if enabled
            if (static::$timestamps) {
                if (!isset($data['created_at'])) {
                    $data['created_at'] = date('Y-m-d H:i:s');
                }
                if (!isset($data['updated_at'])) {
                    $data['updated_at'] = date('Y-m-d H:i:s');
                }
            }

            $processedRecords[] = $data;
        }

        return static::query()->insertMany($processedRecords);
    }

    /**
     * Upsert (insert or update) a record
     *
     * @param array $data Record data
     * @return bool True if operation successful
     * @throws \Exception If database operation fails
     */
    public static function upsert(array $data)
    {
        // Filter fillable fields if defined
        if (!empty(static::$fillable)) {
            $data = array_intersect_key($data, array_flip(static::$fillable));
        }

        // Add timestamps if enabled
        if (static::$timestamps) {
            if (!isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
            $data['updated_at'] = date('Y-m-d H:i:s');
        }

        return static::query()->replace($data);
    }
}
