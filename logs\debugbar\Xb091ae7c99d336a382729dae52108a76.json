{"__meta": {"id": "Xb091ae7c99d336a382729dae52108a76", "datetime": "2025-07-10 09:56:38", "utime": 1752134198.5811, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 14, "messages": [{"message": "Route dispatched", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.543464, "xdebug_link": null}, {"message": "Current Branch: HQ", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.543493, "xdebug_link": null}, {"message": "Middleware: auth", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.54455, "xdebug_link": null}, {"message": "Template: default", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.546847, "xdebug_link": null}, {"message": "Rendering template: home.tpl", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.552839, "xdebug_link": null}, {"message": "Checking privilege: CROSS_BRANCH for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.577289, "xdebug_link": null}, {"message": "Checking privilege: ADMIN for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.577895, "xdebug_link": null}, {"message": "Checking privilege: SKU for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.578333, "xdebug_link": null}, {"message": "Checking privilege: LOGIN for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.578543, "xdebug_link": null}, {"message": "Checking privilege: LOGIN for branch: 2 NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.578822, "xdebug_link": null}, {"message": "Checking privilege: 1 for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.579109, "xdebug_link": null}, {"message": "Checking privilege: 2 for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.579551, "xdebug_link": null}, {"message": "Checking privilege: 3 for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.579997, "xdebug_link": null}, {"message": "Checking privilege: 4 for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134198.580441, "xdebug_link": null}]}, "request": {"$_GET": "array:1 [\n  \"url\" => \"home\"\n]", "$_POST": "[]", "$_COOKIE": "array:1 [\n  \"PHPSESSID\" => \"p1ves5s1kcdrn9949agug85s28\"\n]", "$_SESSION": "array:7 [\n  \"PHPDEBUGBAR_STACK_DATA\" => []\n  \"debugbar_enabled\" => true\n  \"current_branch\" => array:44 [\n    \"id\" => 1\n    \"rev\" => \"b78b\"\n    \"code\" => \"HQ\"\n    \"is_hq\" => 1\n    \"hq_control\" => 1\n    \"has_counter\" => 0\n    \"pair_code\" => \"fb395718-52ba-4cbb-bd76-8fefa27e1762\"\n    \"sync\" => 0\n    \"sync_up\" => 1\n    \"sync_down\" => 1\n    \"ip_address\" => \"127.0.0.1\"\n    \"last_ping\" => \"2019-09-17 10:28:59\"\n    \"description\" => \"LONGWAN HEADQUATERS\"\n    \"group\" => \"HQ\"\n    \"company_no\" => \"515064-H\"\n    \"contact_person\" => \"USER\"\n    \"phone1\" => \"04-4236533\"\n    \"phone2\" => \"04-4232198\"\n    \"fax1\" => \"asdaaaaaazzzzz\"\n    \"email\" => \"<EMAIL>\"\n    \"report_email\" => \"\"\n    \"address\" => \"\"\"\n      NO.57,JALAN SEMBILAN,\\n\n      KAWASAN PERINDUSTRIAN,\\n\n      BAKAR ARANG,\\n\n      08000 SUNGAI PETANI,\\n\n      KEDAH.\n      \"\"\"\n    \"gst_registered\" => 1\n    \"gst_register_no\" => \"000336379904\"\n    \"gst_start_date\" => \"2015-04-01\"\n    \"host\" => \"http://gmark.dyndns.org:3000\"\n    \"master_host\" => \"localhost\"\n    \"anydesk\" => \"*********\"\n    \"rustdesk\" => null\n    \"company_id\" => 3\n    \"e_invoice_enabled\" => 1\n    \"e_invoice_profile_id\" => 1\n    \"e_invoice_country\" => \"MALAYSIA\"\n    \"e_invoice_state\" => \"KEDAH\"\n    \"e_invoice_city\" => \"SUNGAI PETANI\"\n    \"e_invoice_postcode\" => \"08000\"\n    \"e_invoice_address1\" => \"NO. 57\"\n    \"e_invoice_address2\" => \"JALAN SEMBILAN\"\n    \"e_invoice_address3\" => \"KAWASAN PERIDUSTRIAN BAKAR ARANG\"\n    \"e_invoice_contact\" => \"6044246116\"\n    \"active\" => 1\n    \"deleted\" => 0\n    \"created_at\" => \"2017-01-04 14:06:01\"\n    \"updated_at\" => \"2025-06-25 02:14:33\"\n  ]\n  \"current_branch_id\" => 1\n  \"current_branch_code\" => \"HQ\"\n  \"authenticated_web\" => true\n  \"user_id_web\" => 1\n]"}, "time": {"count": 0, "start": 1752134198.523971, "end": 1752134198.581659, "duration": 0.057687997817993164, "duration_str": "57.69ms", "measures": []}, "memory": {"peak_usage": 754608, "peak_usage_str": "737KB"}, "exceptions": {"count": 0, "exceptions": []}, "Config": {"cached": "false", "app": "array:5 [\n  \"env\" => \"development\"\n  \"debug\" => \"false\"\n  \"name\" => \"HQ2 Application\"\n  \"mode\" => \"MAIN\"\n  \"default_branch\" => 1\n]", "database": "array:6 [\n  \"host\" => \"localhost\"\n  \"port\" => \"3306\"\n  \"name\" => \"hq2\"\n  \"user\" => \"root\"\n  \"pass\" => \"\"\n  \"charset\" => \"utf8mb4\"\n]", "session": "array:3 [\n  \"user_id\" => null\n  \"current_branch\" => array:44 [\n    \"id\" => 1\n    \"rev\" => \"b78b\"\n    \"code\" => \"HQ\"\n    \"is_hq\" => 1\n    \"hq_control\" => 1\n    \"has_counter\" => 0\n    \"pair_code\" => \"fb395718-52ba-4cbb-bd76-8fefa27e1762\"\n    \"sync\" => 0\n    \"sync_up\" => 1\n    \"sync_down\" => 1\n    \"ip_address\" => \"127.0.0.1\"\n    \"last_ping\" => \"2019-09-17 10:28:59\"\n    \"description\" => \"LONGWAN HEADQUATERS\"\n    \"group\" => \"HQ\"\n    \"company_no\" => \"515064-H\"\n    \"contact_person\" => \"USER\"\n    \"phone1\" => \"04-4236533\"\n    \"phone2\" => \"04-4232198\"\n    \"fax1\" => \"asdaaaaaazzzzz\"\n    \"email\" => \"<EMAIL>\"\n    \"report_email\" => \"\"\n    \"address\" => \"\"\"\n      NO.57,JALAN SEMBILAN,\\n\n      KAWASAN PERINDUSTRIAN,\\n\n      BAKAR ARANG,\\n\n      08000 SUNGAI PETANI,\\n\n      KEDAH.\n      \"\"\"\n    \"gst_registered\" => 1\n    \"gst_register_no\" => \"000336379904\"\n    \"gst_start_date\" => \"2015-04-01\"\n    \"host\" => \"http://gmark.dyndns.org:3000\"\n    \"master_host\" => \"localhost\"\n    \"anydesk\" => \"*********\"\n    \"rustdesk\" => null\n    \"company_id\" => 3\n    \"e_invoice_enabled\" => 1\n    \"e_invoice_profile_id\" => 1\n    \"e_invoice_country\" => \"MALAYSIA\"\n    \"e_invoice_state\" => \"KEDAH\"\n    \"e_invoice_city\" => \"SUNGAI PETANI\"\n    \"e_invoice_postcode\" => \"08000\"\n    \"e_invoice_address1\" => \"NO. 57\"\n    \"e_invoice_address2\" => \"JALAN SEMBILAN\"\n    \"e_invoice_address3\" => \"KAWASAN PERIDUSTRIAN BAKAR ARANG\"\n    \"e_invoice_contact\" => \"6044246116\"\n    \"active\" => 1\n    \"deleted\" => 0\n    \"created_at\" => \"2017-01-04 14:06:01\"\n    \"updated_at\" => \"2025-06-25 02:14:33\"\n  ]\n  \"login\" => false\n]", "environment": "array:4 [\n  \"PHP_VERSION\" => \"8.2.12\"\n  \"SERVER_SOFTWARE\" => \"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12\"\n  \"REQUEST_METHOD\" => \"GET\"\n  \"REQUEST_URI\" => \"/home\"\n]"}, "pdo": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.004099130630493164, "memory_usage": 194928, "peak_memory_usage": 730304, "statements": [{"sql": "SELECT * FROM `branches` WHERE id = :param_0", "row_count": 1, "stmt_id": "00000000000000160000000000000000", "prepared_stmt": "SELECT * FROM `branches` WHERE id = :param_0", "params": {":param_0": "1"}, "duration": 0.00035691261291503906, "duration_str": "357μs", "memory": 19904, "memory_str": "19.44KB", "end_memory": 567376, "end_memory_str": "554.08KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT `users`.* FROM `users` WHERE users.id = :param_0", "row_count": 1, "stmt_id": "00000000000000190000000000000000", "prepared_stmt": "SELECT `users`.* FROM `users` WHERE users.id = :param_0", "params": {":param_0": "1"}, "duration": 0.00015401840209960938, "duration_str": "154μs", "memory": 17416, "memory_str": "17.01KB", "end_memory": 573520, "end_memory_str": "560.08KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000004a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.0008459091186523438, "duration_str": "846μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 707576, "end_memory_str": "690.99KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000005a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.0004169940948486328, "duration_str": "417μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 725936, "end_memory_str": "708.92KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000005a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.00039315223693847656, "duration_str": "393μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 727344, "end_memory_str": "710.3KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000005a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.00018310546875, "duration_str": "183μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 728736, "end_memory_str": "711.66KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000005a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": "2"}, "duration": 0.0002579689025878906, "duration_str": "258μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 730304, "end_memory_str": "713.19KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000005a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.0002551078796386719, "duration_str": "255μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 715368, "end_memory_str": "698.6KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000005a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.0004169940948486328, "duration_str": "417μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 716728, "end_memory_str": "699.93KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000005a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.0004119873046875, "duration_str": "412μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 718248, "end_memory_str": "701.41KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "000000000000005a0000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.0004069805145263672, "duration_str": "407μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 719608, "end_memory_str": "702.74KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}], "accumulated_duration_str": "4.1ms", "memory_usage_str": "190.36KB", "peak_memory_usage_str": "713.19KB"}}