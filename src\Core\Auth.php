<?php

namespace App\Core;

class Auth
{
    protected static $auth = [];

    private $guard = "web";
    private $user;
    private $privileges = [];

    public function __construct($guard = null)
    {
        if ($guard) $this->guard = $guard;
    }

    public function guard($guard)
    {
        $this->guard = $guard;

        return $this;
    }

    public function getGuard()
    {
        return $this->guard;
    }

    public function check() : bool
    {
        if (isset($_SESSION['authenticated_' . $this->guard]) && $_SESSION['authenticated_' . $this->guard] === true) {
            return true;
        }

        return false;
    }

    public function user()
    {
        if ($this->check()) {
            if (empty($this->user)) {
                
                $userModel = $this->getClass();
                $user = $userModel->getById($_SESSION['user_id_' . $this->guard]);
                
                $this->user = $user;
            }

            return $this->user;
        }

        return null;        
    }

    public function getPrivileges()
    {

    }

    public function authenticate($username, $password)
    {
        $userModel = $this->getClass();
        return $userModel->authenticate($username, $password);
    }

    protected function getClass()
    {
        $class = config('auth.guards.' . $this->guard . '.model');
        $class = str_replace('\\', '\\', $class);

        if (!class_exists($class)) {
            throw new \Exception("User model not found.");
        }

        return new $class;
    }

    /**
     * Set current user in session
     *
     * Sets the current user session and updates
     *
     * @param array $user User data
     */
    public function login($user)
    {
        $_SESSION['authenticated_' . $this->guard] = true;
        $_SESSION['user_id_' . $this->guard] = $user['id'];
    }

    /**
     * Clear user session data
     * 
     * Removes all user-related data from the session.
     */
    public function logout()
    {
        unset($_SESSION['authenticated_' . $this->guard]);
        unset($_SESSION['user_id_' . $this->guard]);
    }

    public function can($privilege, $branch_id = 0)
    {
        if (empty($this->privileges)) $this->getPrivileges();


        return true;
    }

    public static function getAuth($guard = null)
    {
        if (!$guard) {
            $guard = config('auth.default_guard');
        }

        if(!isset(self::$auth[$guard])) {
            self::$auth[$guard] = new Auth($guard);
        }

        return self::$auth[$guard];
    }
}
