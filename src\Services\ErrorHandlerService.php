<?php

namespace App\Services;

use App\Core\Template;
use App\Exceptions\HttpException;
use Exception;
use Throwable;

/**
 * Error Handler Service
 * 
 * Centralized service for handling errors and exceptions with proper
 * formatting for both web and API responses, including logging integration.
 */
class ErrorHandlerService
{
    /**
     * Handle an exception and return appropriate response
     */
    public static function handle(Throwable $exception): void
    {
        // Log the exception
        self::logException($exception);

        // Set appropriate HTTP status code
        $statusCode = self::getStatusCode($exception);
        http_response_code($statusCode);

        if (isApiRequest()) {
            self::handleApiResponse($exception, $statusCode);
        } else {
            self::handleWebResponse($exception, $statusCode);
        }
    }

    /**
     * Handle API response for exceptions
     */
    protected static function handleApiResponse(Throwable $exception, int $statusCode): void
    {
        header('Content-Type: application/json');

        $response = [
            'success' => false,
            'error' => [
                'code' => $statusCode,
                'message' => $exception->getMessage(),
                'type' => self::getErrorType($exception)
            ]
        ];

        // Add additional data for HTTP exceptions
        if ($exception instanceof HttpException) {
            $response['error']['title'] = $exception->getTitle();
            
            $errorData = $exception->getErrorData();
            if (!empty($errorData)) {
                $response['error']['data'] = $errorData;
            }

            // Send custom headers
            foreach ($exception->getHeaders() as $header => $value) {
                header("$header: $value");
            }
        }

        // Add debug information in development mode
        if (config('app.debug', false)) {
            $response['debug'] = [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
    }

    /**
     * Handle web response for exceptions
     */
    protected static function handleWebResponse(Throwable $exception, int $statusCode): void
    {
        // Try to render error template if available
        if (self::hasErrorTemplate($statusCode)) {
            self::renderErrorTemplate($statusCode, $exception);
        } else {
            // Fallback to simple HTML response
            self::renderSimpleErrorPage($statusCode, $exception);
        }
    }

    /**
     * Get HTTP status code from exception
     */
    protected static function getStatusCode(Throwable $exception): int
    {
        if ($exception instanceof HttpException) {
            return $exception->getStatusCode();
        }

        // Default to 500 for unhandled exceptions
        return 500;
    }

    /**
     * Get error type string
     */
    protected static function getErrorType(Throwable $exception): string
    {
        if ($exception instanceof HttpException) {
            return class_basename(get_class($exception));
        }

        return 'Exception';
    }

    /**
     * Log the exception
     */
    protected static function logException(Throwable $exception): void
    {
        if (DebugBarService::isEnabled()) {
            $message = sprintf(
                '%s: %s in %s:%d',
                get_class($exception),
                $exception->getMessage(),
                $exception->getFile(),
                $exception->getLine()
            );

            logger($message, 'error');

            // Log stack trace in debug mode
            if (config('app.debug', false)) {
                logger('Stack trace: ' . $exception->getTraceAsString(), 'error');
            }
        }

        // Also log to PHP error log
        error_log(sprintf(
            '[%s] %s: %s in %s:%d',
            date('Y-m-d H:i:s'),
            get_class($exception),
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine()
        ));
    }

    /**
     * Check if error template exists
     */
    protected static function hasErrorTemplate(int $statusCode): bool
    {
        $templatePath = __DIR__ . "/../../templates/errors/{$statusCode}.tpl";
        return file_exists($templatePath);
    }

    /**
     * Render error template
     */
    protected static function renderErrorTemplate(int $statusCode, Throwable $exception): void
    {
        try {
            $tpl = Template::getTpl();

            $data = [
                'error_code' => $statusCode,
                'error_message' => $exception->getMessage(),
                'error_title' => $exception instanceof HttpException ? $exception->getTitle() : 'Error'
            ];
            
            if (config('app.debug', false)) {
                $data['debug_info'] = [
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine(),
                    'trace' => $exception->getTraceAsString()
                ];
            }

            $tpl->view("errors/{$statusCode}", $data);
        } catch (Exception $e) {
            // Fallback if template rendering fails
            self::renderSimpleErrorPage($statusCode, $exception);
        }
    }

    /**
     * Render simple error page
     */
    protected static function renderSimpleErrorPage(int $statusCode, Throwable $exception): void
    {
        $title = $exception instanceof HttpException ? $exception->getTitle() : 'Error';
        $message = $exception->getMessage();

        echo "<!DOCTYPE html>
<html>
<head>
    <title>{$statusCode} - {$title}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .error-container { max-width: 600px; margin: 0 auto; }
        .error-code { font-size: 72px; color: #dc3545; margin: 0; }
        .error-title { font-size: 24px; margin: 10px 0; }
        .error-message { color: #666; margin: 20px 0; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; }
        .debug-info pre { margin: 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class='error-container'>
        <h1 class='error-code'>{$statusCode}</h1>
        <h2 class='error-title'>{$title}</h2>
        <p class='error-message'>{$message}</p>";

        if (config('app.debug', false)) {
            echo "<div class='debug-info'>
                <strong>Debug Information:</strong><br>
                <strong>File:</strong> {$exception->getFile()}<br>
                <strong>Line:</strong> {$exception->getLine()}<br>
                <strong>Stack Trace:</strong>
                <pre>{$exception->getTraceAsString()}</pre>
            </div>";
        }

        echo "    </div>
</body>
</html>";
    }
}
