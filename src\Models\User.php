<?php

namespace App\Models;

use App\Core\Database;
use App\Core\Model;

/**
 * User Model
 *
 * Handles user-related database operations including authentication,
 * registration, and user data retrieval.
 */
class User extends Model
{
    /** @var Database */
    private $db;

    /** @var string */
    protected static $table = 'users';

    /** @var array */
    protected static $fillable = [
        'name', 'username', 'email', 'password', 'active', 'role'
    ];

    /**
     * User constructor
     *
     * Initializes the database connection for user operations.
     */
    public function __construct()
    {
        $this->db = DB();
    }

    // ===== USER-SPECIFIC STATIC METHODS =====

    /**
     * Override create method to handle password hashing
     *
     * @param array $data User data
     * @return \App\Core\ModelInstance|bool User instance if creation successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public static function create(array $data)
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = md5($data['password']);
        }

        return parent::create($data);
    }

    /**
     * Override updateById method to handle password hashing
     *
     * @param mixed $id User ID
     * @param array $data Data to update
     * @return bool True if update successful
     * @throws \Exception If database operation fails
     */
    public static function updateById($id, array $data)
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = md5($data['password']);
        }

        return parent::updateById($id, $data);
    }

    /**
     * Find user by username
     *
     * @param string $username The username to search for
     * @return \App\Core\ModelInstance|null User instance or null if not found
     * @throws \Exception If database operation fails
     */
    public static function findByUsername($username)
    {
        return static::whereFirst('username', $username);
    }

    /**
     * Find user by email
     *
     * @param string $email The email to search for
     * @return \App\Core\ModelInstance|null User instance or null if not found
     * @throws \Exception If database operation fails
     */
    public static function findByEmail($email)
    {
        return static::whereFirst('email', $email);
    }

    /**
     * Get active users
     *
     * @return array Array of User instances
     * @throws \Exception If database operation fails
     */
    public static function active()
    {
        $records = static::where('active', 1)->get();
        return static::newCollection($records);
    }

    /**
     * Get users by role
     *
     * @param string $role The role to filter by
     * @return array Array of User instances with the specified role
     * @throws \Exception If database operation fails
     */
    public static function byRole($role)
    {
        $records = static::where('role', $role)->get();
        return static::newCollection($records);
    }

    /**
     * Register a new user
     *
     * Creates a new user account with the provided credentials.
     * Password is hashed using MD5 (consider upgrading to bcrypt for security).
     *
     * @param string $name The user's full name
     * @param string $username The user's username (must be unique)
     * @param string $password The user's plain text password
     * @return bool True if registration successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public function register($name, $username, $password)
    {
        $hashedPassword = md5($password);

        return $this->db->table('users')->insert([
            'name' => $name,
            'username' => $username,
            'password' => $hashedPassword,
        ]);
    }

    /**
     * Authenticate user credentials
     *
     * Verifies user username and password against stored credentials.
     * Uses MD5 hashing for password comparison.
     *
     * @param string $username The user's username
     * @param string $password The user's plain text password
     * @return bool True if authentication successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public function authenticate($username, $password)
    {
        $user = $this->db->table('users')->where('username', $username)->first();

        if ($user && md5($password) == $user['password']) {
            return $user;
        }

        return false;
    }

    /**
     * Get user's allowed branches
     *
     * Retrieves the list of branches a user has access to.
     *
     * @param int $userId The user ID
     * @return array Array of allowed branch IDs
     * @throws \Exception If database operation fails
     */
    public function getUserAllowedBranches($userId)
    {
        // // Filter branches based on user permissions using default connection
        $branches = DB()->table('branches')->select('branches.*')
            ->leftJoin('user_branch', 'branches.id', '=', 'user_branch.branch_id')
            ->where('user_id', $userId)->get();

        return $branches;
    }

    /**
     * Create multiple users at once
     *
     * Efficiently inserts multiple user records using the new insertMany method.
     * All users will have their passwords hashed automatically.
     *
     * @param array $users Array of user data arrays
     * @return bool True if all users were created successfully
     * @throws \Exception If data validation fails or database operation fails
     */
    public function createMultipleUsers(array $users)
    {
        if (empty($users)) {
            throw new \Exception("Users array cannot be empty");
        }

        // Validate and prepare user data
        $preparedUsers = [];
        foreach ($users as $index => $user) {
            // Validate required fields
            if (!isset($user['name']) || !isset($user['username']) || !isset($user['password'])) {
                throw new \Exception("User at index {$index} is missing required fields (name, username, password)");
            }

            // Check if username already exists
            if (User::whereFirst($user['username'])) {
                throw new \Exception("Username '{$user['username']}' already exists");
            }

            // Prepare user data with hashed password
            $preparedUsers[] = [
                'name' => $user['name'],
                'username' => $user['username'],
                'password' => md5($user['password']), // Consider upgrading to bcrypt
                'email' => $user['email'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        return $this->db->table('users')->insertMany($preparedUsers);
    }

    /**
     * Bulk import users with transaction support
     *
     * Imports a large number of users using transaction-based batch insertion.
     * Ideal for importing users from CSV files or external systems.
     *
     * @param array $users Array of user data arrays
     * @param int $batchSize Number of users to process per batch (default: 500)
     * @return bool True if all users were imported successfully
     * @throws \Exception If data validation fails or database operation fails
     */
    public function bulkImportUsers(array $users, int $batchSize = 500)
    {
        if (empty($users)) {
            throw new \Exception("Users array cannot be empty");
        }

        // Validate and prepare user data
        $preparedUsers = [];
        $existingUsernames = [];

        // Get all existing usernames to check for duplicates
        $existing = $this->db->table('users')->select('username')->get();
        foreach ($existing as $user) {
            $existingUsernames[] = $user['username'];
        }

        foreach ($users as $index => $user) {
            // Validate required fields
            if (!isset($user['name']) || !isset($user['username']) || !isset($user['password'])) {
                throw new \Exception("User at index {$index} is missing required fields (name, username, password)");
            }

            // Check for duplicate usernames in the import data
            if (in_array($user['username'], $existingUsernames)) {
                throw new \Exception("Username '{$user['username']}' already exists in database");
            }

            // Check for duplicates within the import data itself
            if (in_array($user['username'], array_column($preparedUsers, 'username'))) {
                throw new \Exception("Duplicate username '{$user['username']}' found in import data");
            }

            // Prepare user data with hashed password
            $preparedUsers[] = [
                'name' => $user['name'],
                'username' => $user['username'],
                'password' => md5($user['password']), // Consider upgrading to bcrypt
                'email' => $user['email'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Add to existing usernames to prevent duplicates in subsequent iterations
            $existingUsernames[] = $user['username'];
        }

        return $this->db->table('users')->insertManyWithTransaction($preparedUsers, $batchSize);
    }

    /**
     * Create user branches in bulk
     *
     * Assigns multiple branches to multiple users efficiently.
     *
     * @param array $userBranches Array of user branch data
     * @return bool True if all branches were assigned successfully
     * @throws \Exception If database operation fails
     */
    public function createUserBranchesBulk(array $userBranches)
    {
        if (empty($userBranches)) {
            throw new \Exception("User branches array cannot be empty");
        }

        // Validate data structure
        foreach ($userBranches as $index => $branch) {
            if (!isset($branch['user_id']) || !isset($branch['branch_id'])) {
                throw new \Exception("Privilege at index {$index} is missing required fields (user_id, branch_id)");
            }
        }

        return $this->db->table('user_branch')->insertMany($userBranches);
    }

    /**
     * Create user privileges in bulk
     *
     * Assigns multiple privileges to multiple users efficiently.
     *
     * @param array $userPrivileges Array of user privilege data
     * @return bool True if all privileges were assigned successfully
     * @throws \Exception If database operation fails
     */
    public function createUserPrivilegesBulk(array $userPrivileges)
    {
        if (empty($userPrivileges)) {
            throw new \Exception("User privileges array cannot be empty");
        }

        // Validate data structure
        foreach ($userPrivileges as $index => $privilege) {
            if (!isset($privilege['user_id']) || !isset($privilege['branch_id']) || !isset($privilege['privilege'])) {
                throw new \Exception("Privilege at index {$index} is missing required fields (user_id, branch_id, privilege)");
            }
        }

        return $this->db->table('user_privilege')->insertMany($userPrivileges);
    }

    /**
     * Upsert a single user (insert or update)
     *
     * Uses MySQL's REPLACE INTO to either insert a new user or update an existing one.
     * Requires a unique key (like username or email) to determine if user exists.
     *
     * @param array $userData User data array
     * @return bool True if the operation was successful
     * @throws \Exception If required fields are missing or database operation fails
     */
    public function upsertUser(array $userData)
    {
        // Validate required fields
        if (!isset($userData['username'])) {
            throw new \Exception("Username is required for upsert operation");
        }

        // Prepare user data with timestamps
        $preparedData = [
            'username' => $userData['username'],
            'name' => $userData['name'] ?? '',
            'email' => $userData['email'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add password if provided (hash it)
        if (isset($userData['password'])) {
            $preparedData['password'] = md5($userData['password']);
        }

        // Add created_at for new records (will be ignored for existing records in REPLACE)
        if (!isset($userData['id'])) {
            $preparedData['created_at'] = date('Y-m-d H:i:s');
        } else {
            $preparedData['id'] = $userData['id'];
        }

        return $this->db->table('users')->replace($preparedData);
    }

    /**
     * Upsert multiple users (insert or update)
     *
     * Uses MySQL's REPLACE INTO to efficiently handle multiple user upserts.
     * Ideal for synchronizing user data from external systems.
     *
     * @param array $users Array of user data arrays
     * @return bool True if all operations were successful
     * @throws \Exception If data validation fails or database operation fails
     */
    public function upsertMultipleUsers(array $users)
    {
        if (empty($users)) {
            throw new \Exception("Users array cannot be empty");
        }

        // Validate and prepare user data
        $preparedUsers = [];
        foreach ($users as $index => $user) {
            // Validate required fields
            if (!isset($user['username'])) {
                throw new \Exception("User at index {$index} is missing required username field");
            }

            // Prepare user data
            $preparedUser = [
                'username' => $user['username'],
                'name' => $user['name'] ?? '',
                'email' => $user['email'] ?? null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Add password if provided (hash it)
            if (isset($user['password'])) {
                $preparedUser['password'] = md5($user['password']);
            }

            // Handle ID for existing records or set created_at for new ones
            if (isset($user['id'])) {
                $preparedUser['id'] = $user['id'];
            } else {
                $preparedUser['created_at'] = date('Y-m-d H:i:s');
            }

            $preparedUsers[] = $preparedUser;
        }

        return $this->db->table('users')->replaceMany($preparedUsers);
    }

    /**
     * Bulk synchronize users with external data source
     *
     * Synchronizes a large number of users using transaction-based replace operations.
     * Ideal for importing/updating users from external systems like LDAP, CSV files, etc.
     *
     * @param array $users Array of user data arrays
     * @param int $batchSize Number of users to process per batch (default: 500)
     * @return bool True if all users were synchronized successfully
     * @throws \Exception If data validation fails or database operation fails
     */
    public function bulkSynchronizeUsers(array $users, int $batchSize = 500)
    {
        if (empty($users)) {
            throw new \Exception("Users array cannot be empty");
        }

        // Validate and prepare user data
        $preparedUsers = [];
        foreach ($users as $index => $user) {
            // Validate required fields
            if (!isset($user['username'])) {
                throw new \Exception("User at index {$index} is missing required username field");
            }

            // Prepare user data
            $preparedUser = [
                'username' => $user['username'],
                'name' => $user['name'] ?? '',
                'email' => $user['email'] ?? null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Add password if provided (hash it)
            if (isset($user['password'])) {
                $preparedUser['password'] = md5($user['password']);
            }

            // Handle ID for existing records or set created_at for new ones
            if (isset($user['id'])) {
                $preparedUser['id'] = $user['id'];
            } else {
                $preparedUser['created_at'] = date('Y-m-d H:i:s');
            }

            $preparedUsers[] = $preparedUser;
        }

        return $this->db->table('users')->replaceManyWithTransaction($preparedUsers, $batchSize);
    }
}
