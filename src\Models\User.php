<?php

namespace App\Models;

use App\Core\Database;

/**
 * User Model
 *
 * Handles user-related database operations including authentication,
 * registration, and user data retrieval.
 */
class User
{
    /** @var Database */
    private $db;

    /**
     * User constructor
     *
     * Initializes the database connection for user operations.
     */
    public function __construct()
    {
        $this->db = DB();
    }

    /**
     * Get all users from the database
     *
     * Retrieves all user records from the users table.
     *
     * @return array Array of user records
     */
    public function getAllUsers()
    {
        return $this->db->table('users')->get();
    }

    /**
     * Register a new user
     *
     * Creates a new user account with the provided credentials.
     * Password is hashed using MD5 (consider upgrading to bcrypt for security).
     *
     * @param string $name The user's full name
     * @param string $username The user's username (must be unique)
     * @param string $password The user's plain text password
     * @return bool True if registration successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public function register($name, $username, $password)
    {
        $hashedPassword = md5($password);

        return $this->db->table('users')->insert([
            'name' => $name,
            'username' => $username,
            'password' => $hashedPassword,
        ]);
    }

    /**
     * Authenticate user credentials
     *
     * Verifies user username and password against stored credentials.
     * Uses MD5 hashing for password comparison.
     *
     * @param string $username The user's username
     * @param string $password The user's plain text password
     * @return bool True if authentication successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public function authenticate($username, $password)
    {
        $user = $this->db->table('users')->where('username', $username)->first();

        if ($user && md5($password) == $user['password']) {
            return $user;
        }

        return false;
    }

    /**
     * Get user by id
     *
     * Retrieves user data by id.
     *
     * @param string $id The user's id
     * @return array|false User data or false if not found
     * @throws \Exception If database operation fails
     */
    public function getById($id)
    {
        $user =  $this->db->table('users')
            ->select('users.*')
            ->where('users.id', $id)
            ->first();

        return $user;
    }

    /**
     * Get user by username
     *
     * Retrieves user data by username.
     *
     * @param string $username The user's username
     * @return array|false User data or false if not found
     * @throws \Exception If database operation fails
     */
    public function getUserByUsername($username)
    {
        return $this->db->table('users')
            ->select('users.*')
            ->where('users.username', $username)
            ->first();
    }

    /**
     * Get user's allowed branches
     *
     * Retrieves the list of branches a user has access to.
     *
     * @param int $userId The user ID
     * @return array Array of allowed branch IDs
     * @throws \Exception If database operation fails
     */
    public function getUserAllowedBranches($userId)
    {
        // // Filter branches based on user permissions using default connection
        $branches = DB()->table('branches')->select('branches.*')
            ->leftJoin('user_branch', 'branches.id', '=', 'user_branch.branch_id')
            ->where('user_id', $userId)->get();

        return $branches;
    }

    /**
     * Create multiple users at once
     *
     * Efficiently inserts multiple user records using the new insertMany method.
     * All users will have their passwords hashed automatically.
     *
     * @param array $users Array of user data arrays
     * @return bool True if all users were created successfully
     * @throws \Exception If data validation fails or database operation fails
     */
    public function createMultipleUsers(array $users)
    {
        if (empty($users)) {
            throw new \Exception("Users array cannot be empty");
        }

        // Validate and prepare user data
        $preparedUsers = [];
        foreach ($users as $index => $user) {
            // Validate required fields
            if (!isset($user['name']) || !isset($user['username']) || !isset($user['password'])) {
                throw new \Exception("User at index {$index} is missing required fields (name, username, password)");
            }

            // Check if username already exists
            if ($this->getUserByUsername($user['username'])) {
                throw new \Exception("Username '{$user['username']}' already exists");
            }

            // Prepare user data with hashed password
            $preparedUsers[] = [
                'name' => $user['name'],
                'username' => $user['username'],
                'password' => md5($user['password']), // Consider upgrading to bcrypt
                'email' => $user['email'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        return $this->db->table('users')->insertMany($preparedUsers);
    }

    /**
     * Bulk import users with transaction support
     *
     * Imports a large number of users using transaction-based batch insertion.
     * Ideal for importing users from CSV files or external systems.
     *
     * @param array $users Array of user data arrays
     * @param int $batchSize Number of users to process per batch (default: 500)
     * @return bool True if all users were imported successfully
     * @throws \Exception If data validation fails or database operation fails
     */
    public function bulkImportUsers(array $users, int $batchSize = 500)
    {
        if (empty($users)) {
            throw new \Exception("Users array cannot be empty");
        }

        // Validate and prepare user data
        $preparedUsers = [];
        $existingUsernames = [];

        // Get all existing usernames to check for duplicates
        $existing = $this->db->table('users')->select('username')->get();
        foreach ($existing as $user) {
            $existingUsernames[] = $user['username'];
        }

        foreach ($users as $index => $user) {
            // Validate required fields
            if (!isset($user['name']) || !isset($user['username']) || !isset($user['password'])) {
                throw new \Exception("User at index {$index} is missing required fields (name, username, password)");
            }

            // Check for duplicate usernames in the import data
            if (in_array($user['username'], $existingUsernames)) {
                throw new \Exception("Username '{$user['username']}' already exists in database");
            }

            // Check for duplicates within the import data itself
            if (in_array($user['username'], array_column($preparedUsers, 'username'))) {
                throw new \Exception("Duplicate username '{$user['username']}' found in import data");
            }

            // Prepare user data with hashed password
            $preparedUsers[] = [
                'name' => $user['name'],
                'username' => $user['username'],
                'password' => md5($user['password']), // Consider upgrading to bcrypt
                'email' => $user['email'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Add to existing usernames to prevent duplicates in subsequent iterations
            $existingUsernames[] = $user['username'];
        }

        return $this->db->table('users')->insertManyWithTransaction($preparedUsers, $batchSize);
    }

    /**
     * Create user privileges in bulk
     *
     * Assigns multiple privileges to multiple users efficiently.
     *
     * @param array $userPrivileges Array of user privilege data
     * @return bool True if all privileges were assigned successfully
     * @throws \Exception If database operation fails
     */
    public function createUserPrivilegesBulk(array $userPrivileges)
    {
        if (empty($userPrivileges)) {
            throw new \Exception("User privileges array cannot be empty");
        }

        // Validate data structure
        foreach ($userPrivileges as $index => $privilege) {
            if (!isset($privilege['user_id']) || !isset($privilege['privilege_id'])) {
                throw new \Exception("Privilege at index {$index} is missing required fields (user_id, privilege_id)");
            }
        }

        return $this->db->table('user_privilege')->insertMany($userPrivileges);
    }

    /**
     * Upsert a single user (insert or update)
     *
     * Uses MySQL's REPLACE INTO to either insert a new user or update an existing one.
     * Requires a unique key (like username or email) to determine if user exists.
     *
     * @param array $userData User data array
     * @return bool True if the operation was successful
     * @throws \Exception If required fields are missing or database operation fails
     */
    public function upsertUser(array $userData)
    {
        // Validate required fields
        if (!isset($userData['username'])) {
            throw new \Exception("Username is required for upsert operation");
        }

        // Prepare user data with timestamps
        $preparedData = [
            'username' => $userData['username'],
            'name' => $userData['name'] ?? '',
            'email' => $userData['email'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add password if provided (hash it)
        if (isset($userData['password'])) {
            $preparedData['password'] = md5($userData['password']);
        }

        // Add created_at for new records (will be ignored for existing records in REPLACE)
        if (!isset($userData['id'])) {
            $preparedData['created_at'] = date('Y-m-d H:i:s');
        } else {
            $preparedData['id'] = $userData['id'];
        }

        return $this->db->table('users')->replace($preparedData);
    }

    /**
     * Upsert multiple users (insert or update)
     *
     * Uses MySQL's REPLACE INTO to efficiently handle multiple user upserts.
     * Ideal for synchronizing user data from external systems.
     *
     * @param array $users Array of user data arrays
     * @return bool True if all operations were successful
     * @throws \Exception If data validation fails or database operation fails
     */
    public function upsertMultipleUsers(array $users)
    {
        if (empty($users)) {
            throw new \Exception("Users array cannot be empty");
        }

        // Validate and prepare user data
        $preparedUsers = [];
        foreach ($users as $index => $user) {
            // Validate required fields
            if (!isset($user['username'])) {
                throw new \Exception("User at index {$index} is missing required username field");
            }

            // Prepare user data
            $preparedUser = [
                'username' => $user['username'],
                'name' => $user['name'] ?? '',
                'email' => $user['email'] ?? null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Add password if provided (hash it)
            if (isset($user['password'])) {
                $preparedUser['password'] = md5($user['password']);
            }

            // Handle ID for existing records or set created_at for new ones
            if (isset($user['id'])) {
                $preparedUser['id'] = $user['id'];
            } else {
                $preparedUser['created_at'] = date('Y-m-d H:i:s');
            }

            $preparedUsers[] = $preparedUser;
        }

        return $this->db->table('users')->replaceMany($preparedUsers);
    }

    /**
     * Bulk synchronize users with external data source
     *
     * Synchronizes a large number of users using transaction-based replace operations.
     * Ideal for importing/updating users from external systems like LDAP, CSV files, etc.
     *
     * @param array $users Array of user data arrays
     * @param int $batchSize Number of users to process per batch (default: 500)
     * @return bool True if all users were synchronized successfully
     * @throws \Exception If data validation fails or database operation fails
     */
    public function bulkSynchronizeUsers(array $users, int $batchSize = 500)
    {
        if (empty($users)) {
            throw new \Exception("Users array cannot be empty");
        }

        // Validate and prepare user data
        $preparedUsers = [];
        foreach ($users as $index => $user) {
            // Validate required fields
            if (!isset($user['username'])) {
                throw new \Exception("User at index {$index} is missing required username field");
            }

            // Prepare user data
            $preparedUser = [
                'username' => $user['username'],
                'name' => $user['name'] ?? '',
                'email' => $user['email'] ?? null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Add password if provided (hash it)
            if (isset($user['password'])) {
                $preparedUser['password'] = md5($user['password']);
            }

            // Handle ID for existing records or set created_at for new ones
            if (isset($user['id'])) {
                $preparedUser['id'] = $user['id'];
            } else {
                $preparedUser['created_at'] = date('Y-m-d H:i:s');
            }

            $preparedUsers[] = $preparedUser;
        }

        return $this->db->table('users')->replaceManyWithTransaction($preparedUsers, $batchSize);
    }

    /**
     * Synchronize user settings/preferences
     *
     * Uses replace functionality to update user settings, creating new entries
     * or updating existing ones based on user_id and setting_key combination.
     *
     * @param array $userSettings Array of user setting data
     * @return bool True if all settings were synchronized successfully
     * @throws \Exception If database operation fails
     */
    public function synchronizeUserSettings(array $userSettings)
    {
        if (empty($userSettings)) {
            throw new \Exception("User settings array cannot be empty");
        }

        // Validate data structure
        foreach ($userSettings as $index => $setting) {
            if (!isset($setting['user_id']) || !isset($setting['setting_key'])) {
                throw new \Exception("Setting at index {$index} is missing required fields (user_id, setting_key)");
            }
        }

        // Add timestamps to all settings
        $preparedSettings = [];
        foreach ($userSettings as $setting) {
            $preparedSettings[] = array_merge($setting, [
                'updated_at' => date('Y-m-d H:i:s'),
                'created_at' => $setting['created_at'] ?? date('Y-m-d H:i:s')
            ]);
        }

        return $this->db->table('user_settings')->replaceMany($preparedSettings);
    }
}
