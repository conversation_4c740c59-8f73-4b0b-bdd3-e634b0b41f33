<?php

namespace App\Controllers\Vendor;

use App\Controller\Controller;
use App\Models\User;
use App\Models\Branch;

/**
 * Authentication Controller
 *
 * Handles user authentication operations including login, logout, and registration.
 * Manages session state, branch selection, and redirects users based on authentication status.
 */
class AuthController extends Controller
{
    /**
     * Handle user login
     *
     * Processes both GET and POST requests for user login:
     * - GET: Displays the login form with branch selection
     * - POST: Validates credentials, sets branch, and creates user session
     *
     * On successful login, redirects to /home and sets session variables.
     * On failed login, redisplays form with error message.
     */
    public function login()
    {
        if (auth()->check()) {
            redirect(route('vendor.home'));
        }

        $error = null;
        $selected_branch = config('app.default_branch', 1);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') { 
            $username = $_POST['username'];
            $password = $_POST['password'];
            $branchId = isset($_POST['branch_id']) ? (int)$_POST['branch_id'] : null;

            if ($user = auth()->authenticate($username, $password)) {
                if ($this->branchService->userHasAccessToBranch($user['id'], $branchId)) {
                    $this->branchService->switchBranch($branchId);

                    auth()->login($user);

                    redirect(route('vendor.home'));
                } else {
                    $error = 'Access denied to selected branch. Please check your username or password';
                    $selected_branch = $branchId;
                }
            } else {
                $error = 'Invalid username or password';
                $selected_branch = $branchId;
            }
        }

        // GET request - show login form with branches
        $branchModel = new Branch();
        $branches = $branchModel->getAllActiveBranches();

        $data = [
            'branches' => $branches,
        ];

        if ($error) $data['error'] = $error;
        if ($selected_branch) $data['selected_branch'] = $selected_branch;
        
        $this->view('login', $data);
    }

    /**
     * Handle user logout
     *
     * Destroys user session data including branch information and redirects to home page.
     * Removes authentication status, username, and branch data from session.
     */
    public function logout()
    {
        // Clear user session data
        auth()->logout();

        // Clear branch session data
        $this->branchService->clearBranchSession();

        redirect(route('vendor.home'));
    }
}
