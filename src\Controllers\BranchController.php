<?php

namespace App\Controllers;

use App\Controller\Controller;
use App\Models\Branch;

/**
 * Branch Controller
 * 
 * Handles branch-related operations including branch switching,
 * branch selection, and branch information display.
 */
class BranchController extends Controller
{
    /** @var Branch */
    private $branchModel;

    /**
     * BranchController constructor
     * 
     * Initializes the branch controller with required services.
     */
    public function __construct()
    {
        parent::__construct();
        $this->branchModel = new Branch();
    }

    /**
     * Switch user's current branch
     * 
     * Handles POST requests to switch the user's current branch.
     * Validates the branch and user permissions before switching.
     * 
     * Returns JSON response for AJAX requests or redirects for form submissions.
     */
    public function switch()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('HTTP/1.1 405 Method Not Allowed');
            echo json_encode(['error' => 'Method not allowed']);
            exit;
        }

        $branchId = isset($_POST['branch_id']) ? (int)$_POST['branch_id'] : null;
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        if (!$branchId) {
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode(['error' => 'Branch ID is required']);
                exit;
            } else {
                redirect('/home?error=invalid_branch');
            }
        }

        try {
            $success = $this->branchService->switchBranch($branchId);
            
            if ($success) {
                $currentBranch = $this->branchService->getCurrentBranch();
                
                if ($isAjax) {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => 'Branch switched successfully',
                        'branch' => $currentBranch
                    ]);
                    exit;
                } else {
                    redirect('/home?success=branch_switched');
                }
            }
        } catch (\Exception $e) {
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode(['error' => $e->getMessage()]);
                exit;
            } else {
                redirect('/home?error=' . urlencode($e->getMessage()));
            }
        }
    }

    /**
     * Get available branches for current user
     * 
     * Returns JSON list of branches available to the current user.
     * Used for AJAX branch selection dropdowns.
     */
    public function getAvailable()
    {
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        $branches = $this->branchService->getAvailableBranches($userId);
        $currentBranch = $this->branchService->getCurrentBranch();
        
        jsonSuccess([
            'branches' => $branches,
            'current_branch' => $currentBranch
        ]);
    }

    /**
     * Display branch selection page
     * 
     * Shows a dedicated page for branch selection with detailed information
     * about each available branch.
     */
    public function select()
    {
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        $branches = $this->branchService->getAvailableBranches($userId);
        $currentBranch = $this->branchService->getCurrentBranch();
        
        $this->view('branch/select', [
            'branches' => $branches,
            'current_branch' => $currentBranch
        ]);
    }

    /**
     * Get current branch information
     * 
     * Returns JSON information about the current branch including
     * configuration and settings.
     */
    public function current()
    {
        $currentBranch = $this->branchService->getCurrentBranch();

        jsonSuccess([
            'branch' => $currentBranch,            
        ]);
    }

    /**
     * Display branch management page (admin only)
     * 
     * Shows administrative interface for managing branches.
     * Requires admin privileges.
     */
    public function manage()
    {
        // Check if user is admin (implement your admin check logic)
        if (!$this->isAdmin()) {
            header('HTTP/1.1 403 Forbidden');
            $this->view('error/403');
            return;
        }

        $branches = $this->branchModel->getAllActiveBranches();
        
        $this->view('admin/branches', [
            'branches' => $branches
        ]);
    }

    /**
     * Create new branch (admin only)
     * 
     * Handles creation of new branches through admin interface.
     */
    public function create()
    {
        if (!$this->isAdmin()) {
            header('HTTP/1.1 403 Forbidden');
            echo json_encode(['error' => 'Access denied']);
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('HTTP/1.1 405 Method Not Allowed');
            echo json_encode(['error' => 'Method not allowed']);
            exit;
        }

        try {
            $data = [
                'code' => $_POST['code'] ?? '',
                'description' => $_POST['description'] ?? '',                
            ];

            $success = $this->branchModel->createBranch($data);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => $success,
                'message' => $success ? 'Branch created successfully' : 'Failed to create branch'
            ]);
            exit;
            
        } catch (\Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['error' => $e->getMessage()]);
            exit;
        }
    }

    /**
     * Check if current user is admin
     * 
     * Implement your admin check logic here.
     * This is a placeholder implementation.
     * 
     * @return bool True if user is admin, false otherwise
     */
    private function isAdmin()
    {
        // Implement your admin check logic
        // For example, check user role in database
        if (!isset($_SESSION['user_id'])) {
            return false;
        }

        $user = DB()->table('users')->where('id', $_SESSION['user_id'])->first();
        return $user && isset($user['role']) && $user['role'] === 'admin';
    }
}
