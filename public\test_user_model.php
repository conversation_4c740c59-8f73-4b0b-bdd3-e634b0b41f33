<?php
/**
 * Test file for User model static methods
 * 
 * This file demonstrates the new Laravel-like static methods for the User model
 */

require_once '../vendor/autoload.php';
require_once '../src/helper.php';

use App\Models\User;

// Set content type to HTML for better output formatting
header('Content-Type: text/html; charset=UTF-8');

// Simple styling
echo '<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #333; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    .success { color: green; }
    .error { color: red; }
    .example { background: #e9f7fe; padding: 15px; border-left: 4px solid #2196F3; margin-bottom: 20px; }
    .code { font-family: monospace; background: #f0f0f0; padding: 2px 4px; }
</style>';

echo '<h1>User Model Static Methods Test</h1>';

// Function to display test results
function displayTest($title, $code, $result) {
    echo '<div class="example">';
    echo "<h3>{$title}</h3>";
    echo '<pre class="code">' . htmlspecialchars($code) . '</pre>';
    echo '<h4>Result:</h4>';
    echo '<pre>';
    print_r($result);
    echo '</pre>';
    echo '</div>';
}

// Test 1: Find user by ID
try {
    $code = 'User::find(1)';
    $user = User::find(1);
    displayTest('Find User by ID', $code, $user);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 2: Find multiple users
try {
    $code = 'User::findMany([1, 2, 3])';
    $users = User::findMany([1, 2, 3]);
    displayTest('Find Multiple Users', $code, $users);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 3: Get all users
try {
    $code = 'User::all()';
    $allUsers = User::all();
    // Limit display to first 3 users to avoid overwhelming output
    $displayUsers = array_slice($allUsers, 0, 3);
    if (count($allUsers) > 3) {
        $displayUsers[] = '... ' . (count($allUsers) - 3) . ' more users ...';
    }
    displayTest('Get All Users', $code, $displayUsers);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 4: Where clause
try {
    $code = 'User::where(\'username\', \'admin\')->first()';
    $adminUser = User::where('username', 'admin')->first();
    displayTest('Find User by Username (Where)', $code, $adminUser);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 4b: User-specific methods
try {
    $code = 'User::findByUsername(\'admin\')';
    $adminUser = User::findByUsername('admin');
    displayTest('Find User by Username (Specific Method)', $code, $adminUser);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 4c: Active users
try {
    $code = 'User::active()';
    $activeUsers = User::active();
    // Limit display to first 2 users
    $displayUsers = array_slice($activeUsers, 0, 2);
    if (count($activeUsers) > 2) {
        $displayUsers[] = '... ' . (count($activeUsers) - 2) . ' more active users ...';
    }
    displayTest('Get Active Users', $code, $displayUsers);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 5: Count users
try {
    $code = 'User::count()';
    $count = User::count();
    displayTest('Count Total Users', $code, $count);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 6: Latest user
try {
    $code = 'User::latest()';
    $latestUser = User::latest();
    displayTest('Get Latest User', $code, $latestUser);
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
}

// Test 7: Create user (commented out to prevent actual database changes)
echo '<div class="example">';
echo '<h3>Create User (Example Only)</h3>';
echo '<pre class="code">User::create([
    \'name\' => \'New User\',
    \'username\' => \'newuser\',
    \'password\' => \'password123\',
    \'email\' => \'<EMAIL>\'
])</pre>';
echo '<h4>Result:</h4>';
echo '<pre>// Not executed to prevent database changes</pre>';
echo '</div>';

// Test 8: Update user (commented out to prevent actual database changes)
echo '<div class="example">';
echo '<h3>Update User (Example Only)</h3>';
echo '<pre class="code">User::updateById(1, [
    \'name\' => \'Updated Name\',
    \'email\' => \'<EMAIL>\'
])</pre>';
echo '<h4>Result:</h4>';
echo '<pre>// Not executed to prevent database changes</pre>';
echo '</div>';

// Test 9: Delete user (commented out to prevent actual database changes)
echo '<div class="example">';
echo '<h3>Delete User (Example Only)</h3>';
echo '<pre class="code">User::destroy(123)</pre>';
echo '<h4>Result:</h4>';
echo '<pre>// Not executed to prevent database changes</pre>';
echo '</div>';

// Display usage examples
echo '<h2>Usage Examples</h2>';

echo '<div class="example">';
echo '<h3>Basic CRUD Operations</h3>';
echo '<pre class="code">// Find a user
$user = User::find(1);

// Find or fail (throws exception if not found)
$user = User::findOrFail(1);

// Create a user
$success = User::create([
    \'name\' => \'John Doe\',
    \'username\' => \'johndoe\',
    \'password\' => \'secret123\',
    \'email\' => \'<EMAIL>\'
]);

// Update a user
$success = User::updateById(1, [
    \'name\' => \'John Updated\',
    \'email\' => \'<EMAIL>\'
]);

// Delete a user
$success = User::destroy(1);</pre>';
echo '</div>';

echo '<div class="example">';
echo '<h3>Query Building</h3>';
echo '<pre class="code">// Basic where clause
$users = User::where(\'active\', 1)->get();

// Complex queries
$users = User::where(\'created_at\', \'>\', \'2023-01-01\')
    ->where(\'role\', \'admin\')
    ->orderBy(\'name\')
    ->limit(10)
    ->get();

// First matching record
$user = User::where(\'email\', \'<EMAIL>\')->first();

// Count with conditions
$count = User::where(\'active\', 1)->count();</pre>';
echo '</div>';

echo '<h2>Benefits</h2>';
echo '<ul>';
echo '<li>Laravel-like syntax for familiar developer experience</li>';
echo '<li>Static methods for simpler, more readable code</li>';
echo '<li>Consistent API across the application</li>';
echo '<li>Reduced code duplication</li>';
echo '<li>Easier to maintain and extend</li>';
echo '</ul>';

echo '<p class="success">All tests completed!</p>';
?>
