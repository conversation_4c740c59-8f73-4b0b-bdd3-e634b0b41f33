{"__meta": {"id": "Xdf8603f53977e36fe75b651bcc93fef5", "datetime": "2025-07-10 10:00:04", "utime": 1752134404.685287, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 14, "messages": [{"message": "Route dispatched", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.680981, "xdebug_link": null}, {"message": "Current Branch: HQ", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.680985, "xdebug_link": null}, {"message": "Middleware: auth", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.681161, "xdebug_link": null}, {"message": "Template: default", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.681264, "xdebug_link": null}, {"message": "Rendering template: home.tpl", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.683406, "xdebug_link": null}, {"message": "Checking privilege: CROSS_BRANCH for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.684327, "xdebug_link": null}, {"message": "Checking privilege: ADMIN for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.684489, "xdebug_link": null}, {"message": "Checking privilege: SKU for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.684583, "xdebug_link": null}, {"message": "Checking privilege: LOGIN for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.684669, "xdebug_link": null}, {"message": "Checking privilege: LOGIN for branch: 2 NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.684788, "xdebug_link": null}, {"message": "Checking privilege: 1 for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.684879, "xdebug_link": null}, {"message": "Checking privilege: 2 for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.684962, "xdebug_link": null}, {"message": "Checking privilege: 3 for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.685041, "xdebug_link": null}, {"message": "Checking privilege: 4 for branch:  NO", "message_html": null, "is_string": true, "label": "info", "time": 1752134404.68512, "xdebug_link": null}]}, "request": {"$_GET": "array:1 [\n  \"url\" => \"home\"\n]", "$_POST": "[]", "$_COOKIE": "array:1 [\n  \"PHPSESSID\" => \"p1ves5s1kcdrn9949agug85s28\"\n]", "$_SESSION": "array:7 [\n  \"PHPDEBUGBAR_STACK_DATA\" => []\n  \"debugbar_enabled\" => true\n  \"current_branch\" => array:44 [\n    \"id\" => 1\n    \"rev\" => \"b78b\"\n    \"code\" => \"HQ\"\n    \"is_hq\" => 1\n    \"hq_control\" => 1\n    \"has_counter\" => 0\n    \"pair_code\" => \"fb395718-52ba-4cbb-bd76-8fefa27e1762\"\n    \"sync\" => 0\n    \"sync_up\" => 1\n    \"sync_down\" => 1\n    \"ip_address\" => \"127.0.0.1\"\n    \"last_ping\" => \"2019-09-17 10:28:59\"\n    \"description\" => \"LONGWAN HEADQUATERS\"\n    \"group\" => \"HQ\"\n    \"company_no\" => \"515064-H\"\n    \"contact_person\" => \"USER\"\n    \"phone1\" => \"04-4236533\"\n    \"phone2\" => \"04-4232198\"\n    \"fax1\" => \"asdaaaaaazzzzz\"\n    \"email\" => \"<EMAIL>\"\n    \"report_email\" => \"\"\n    \"address\" => \"\"\"\n      NO.57,JALAN SEMBILAN,\\n\n      KAWASAN PERINDUSTRIAN,\\n\n      BAKAR ARANG,\\n\n      08000 SUNGAI PETANI,\\n\n      KEDAH.\n      \"\"\"\n    \"gst_registered\" => 1\n    \"gst_register_no\" => \"000336379904\"\n    \"gst_start_date\" => \"2015-04-01\"\n    \"host\" => \"http://gmark.dyndns.org:3000\"\n    \"master_host\" => \"localhost\"\n    \"anydesk\" => \"*********\"\n    \"rustdesk\" => null\n    \"company_id\" => 3\n    \"e_invoice_enabled\" => 1\n    \"e_invoice_profile_id\" => 1\n    \"e_invoice_country\" => \"MALAYSIA\"\n    \"e_invoice_state\" => \"KEDAH\"\n    \"e_invoice_city\" => \"SUNGAI PETANI\"\n    \"e_invoice_postcode\" => \"08000\"\n    \"e_invoice_address1\" => \"NO. 57\"\n    \"e_invoice_address2\" => \"JALAN SEMBILAN\"\n    \"e_invoice_address3\" => \"KAWASAN PERIDUSTRIAN BAKAR ARANG\"\n    \"e_invoice_contact\" => \"6044246116\"\n    \"active\" => 1\n    \"deleted\" => 0\n    \"created_at\" => \"2017-01-04 14:06:01\"\n    \"updated_at\" => \"2025-06-25 02:14:33\"\n  ]\n  \"current_branch_id\" => 1\n  \"current_branch_code\" => \"HQ\"\n  \"authenticated_web\" => true\n  \"user_id_web\" => 1\n]"}, "time": {"count": 0, "start": 1752134404.677385, "end": 1752134404.685625, "duration": 0.008239984512329102, "duration_str": "8.24ms", "measures": []}, "memory": {"peak_usage": 693952, "peak_usage_str": "678KB"}, "exceptions": {"count": 0, "exceptions": []}, "Config": {"cached": "false", "app": "array:5 [\n  \"env\" => \"development\"\n  \"debug\" => \"false\"\n  \"name\" => \"HQ2 Application\"\n  \"mode\" => \"MAIN\"\n  \"default_branch\" => 1\n]", "database": "array:6 [\n  \"host\" => \"localhost\"\n  \"port\" => \"3306\"\n  \"name\" => \"hq2\"\n  \"user\" => \"root\"\n  \"pass\" => \"\"\n  \"charset\" => \"utf8mb4\"\n]", "session": "array:3 [\n  \"user_id\" => null\n  \"current_branch\" => array:44 [\n    \"id\" => 1\n    \"rev\" => \"b78b\"\n    \"code\" => \"HQ\"\n    \"is_hq\" => 1\n    \"hq_control\" => 1\n    \"has_counter\" => 0\n    \"pair_code\" => \"fb395718-52ba-4cbb-bd76-8fefa27e1762\"\n    \"sync\" => 0\n    \"sync_up\" => 1\n    \"sync_down\" => 1\n    \"ip_address\" => \"127.0.0.1\"\n    \"last_ping\" => \"2019-09-17 10:28:59\"\n    \"description\" => \"LONGWAN HEADQUATERS\"\n    \"group\" => \"HQ\"\n    \"company_no\" => \"515064-H\"\n    \"contact_person\" => \"USER\"\n    \"phone1\" => \"04-4236533\"\n    \"phone2\" => \"04-4232198\"\n    \"fax1\" => \"asdaaaaaazzzzz\"\n    \"email\" => \"<EMAIL>\"\n    \"report_email\" => \"\"\n    \"address\" => \"\"\"\n      NO.57,JALAN SEMBILAN,\\n\n      KAWASAN PERINDUSTRIAN,\\n\n      BAKAR ARANG,\\n\n      08000 SUNGAI PETANI,\\n\n      KEDAH.\n      \"\"\"\n    \"gst_registered\" => 1\n    \"gst_register_no\" => \"000336379904\"\n    \"gst_start_date\" => \"2015-04-01\"\n    \"host\" => \"http://gmark.dyndns.org:3000\"\n    \"master_host\" => \"localhost\"\n    \"anydesk\" => \"*********\"\n    \"rustdesk\" => null\n    \"company_id\" => 3\n    \"e_invoice_enabled\" => 1\n    \"e_invoice_profile_id\" => 1\n    \"e_invoice_country\" => \"MALAYSIA\"\n    \"e_invoice_state\" => \"KEDAH\"\n    \"e_invoice_city\" => \"SUNGAI PETANI\"\n    \"e_invoice_postcode\" => \"08000\"\n    \"e_invoice_address1\" => \"NO. 57\"\n    \"e_invoice_address2\" => \"JALAN SEMBILAN\"\n    \"e_invoice_address3\" => \"KAWASAN PERIDUSTRIAN BAKAR ARANG\"\n    \"e_invoice_contact\" => \"6044246116\"\n    \"active\" => 1\n    \"deleted\" => 0\n    \"created_at\" => \"2017-01-04 14:06:01\"\n    \"updated_at\" => \"2025-06-25 02:14:33\"\n  ]\n  \"login\" => false\n]", "environment": "array:4 [\n  \"PHP_VERSION\" => \"8.2.12\"\n  \"SERVER_SOFTWARE\" => \"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12\"\n  \"REQUEST_METHOD\" => \"GET\"\n  \"REQUEST_URI\" => \"/home\"\n]"}, "pdo": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.0012326240539550781, "memory_usage": 194928, "peak_memory_usage": 693896, "statements": [{"sql": "SELECT * FROM `branches` WHERE id = :param_0", "row_count": 1, "stmt_id": "00000000000000160000000000000000", "prepared_stmt": "SELECT * FROM `branches` WHERE id = :param_0", "params": {":param_0": "1"}, "duration": 0.00034689903259277344, "duration_str": "347μs", "memory": 19904, "memory_str": "19.44KB", "end_memory": 567376, "end_memory_str": "554.08KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT `users`.* FROM `users` WHERE users.id = :param_0", "row_count": 1, "stmt_id": "00000000000000190000000000000000", "prepared_stmt": "SELECT `users`.* FROM `users` WHERE users.id = :param_0", "params": {":param_0": "1"}, "duration": 0.00011992454528808594, "duration_str": "120μs", "memory": 17416, "memory_str": "17.01KB", "end_memory": 573520, "end_memory_str": "560.08KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.00013399124145507812, "duration_str": "134μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 671616, "end_memory_str": "655.88KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 0.00010800361633300781, "duration_str": "108μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 689528, "end_memory_str": "673.37KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 7.605552673339844e-05, "duration_str": "76μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 690936, "end_memory_str": "674.74KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 7.104873657226562e-05, "duration_str": "71μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 692328, "end_memory_str": "676.1KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": "2"}, "duration": 0.0001049041748046875, "duration_str": "105μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 693896, "end_memory_str": "677.63KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 7.390975952148438e-05, "duration_str": "74μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 678960, "end_memory_str": "663.05KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 6.794929504394531e-05, "duration_str": "68μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 680320, "end_memory_str": "664.38KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 6.4849853515625e-05, "duration_str": "65μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 681840, "end_memory_str": "665.86KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}, {"sql": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "row_count": 0, "stmt_id": "00000000000000410000000000000000", "prepared_stmt": "SELECT * FROM `user_privilege` WHERE user_id = :param_0 AND branch_id = :param_1", "params": {":param_0": "1", ":param_1": ""}, "duration": 6.508827209472656e-05, "duration_str": "65μs", "memory": 17512, "memory_str": "17.1KB", "end_memory": 683200, "end_memory_str": "667.19KB", "is_success": true, "error_code": 0, "error_message": "", "connection": "default"}], "accumulated_duration_str": "1.23ms", "memory_usage_str": "190.36KB", "peak_memory_usage_str": "677.63KB"}}