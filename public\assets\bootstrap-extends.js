(function ($) {
	$.alert = function (o) {
		var defaults={
				type: 'success',
				backdrop:'static',
				keyboard:true,
				show:false,
				onShown: function () { tmpl.find("#onOK").focus(); },
				onClose: function () {  },
				width:300
			};

		var options = $.extend(true, {}, defaults, o);

		var tmpl = [
			// tabindex is required for focus
			'<div class="modal" tabindex="-1">',
				'<div class="alert alert-'+options.type+'" style="margin: 0px;padding-right:14px;">',
				'<button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="right: -5px;">&times;</button>',
				'<div class="clearfix"></div>',
					'<p>'+options.message+'</p>',
					'<button id="onOK" class="btn btn-success pull-right" data-dismiss="modal">OK</button>',
					'<div class="clearfix"></div>',
				'</div>',
			'</div>'
		].join('');

		tmpl=$(tmpl);

		if(options.width!=null){
			tmpl.attr('data-width',options.width);
		}

		tmpl.modal({
			backdrop:options.backdrop,
			keyboard:options.keyboard,
			show:options.show
		});

		tmpl.on({
			'show.bs.modal':function(){ //console.log('show');

			},
			'shown.bs.modal':function(){ //console.log('shown');
				$.proxy(options.onShown(), tmpl);
			},
			'hide.bs.modal':function(){ //console.log('hide');
				//$.proxy(options.onClose(), tmpl);
			},
			'hidden.bs.modal':function(){ //console.log('hidden');
				$.proxy(options.onClose(), tmpl);
			},
		}).modal('show');

	};

	$.confirm = function (o) {
		var defaults={
				type: 'warning',
				backdrop:'static',
				keyboard:false,
				show:false,
				onShown: function () {  },
				onClose: function () {  },
				onClosed: function () {  },
				onConfirm: function () {  },
				onCancel: function() {  },
				width:400
			};

		var options = $.extend(true, {}, defaults, o);

		var tmpl = [
			// tabindex is required for focus
			'<div class="modal" tabindex="-1">',
			'<div class="alert alert-'+options.type+'" style="margin: 0px;padding-right:14px;">',
			// '<button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="right: -5px;">&times;</button>',
			'<div class="clearfix"></div>',
			'<p>'+options.message+'</p>',
			'<br/><div>',
			'<button id="onCancel" class="btn btn-warning">No</button>',
			'<button id="onConfirm" class="btn btn-success pull-right">Yes</button>&nbsp;',
			'</div>',
			'<div class="clearfix"></div>',
			'</div>',
			'</div>'
		].join('');

		tmpl=$(tmpl);

		if(options.width!=null){
			tmpl.attr('data-width',options.width);
		}

		tmpl.modal({
			backdrop:options.backdrop,
			keyboard:options.keyboard,
			show:options.show
		});

		tmpl.on({
			'show.bs.modal':function(){

			},
			'shown.bs.modal':function(){
				$.proxy(options.onShown(), this);
			},
			'hide.bs.modal':function(){
				$.proxy(options.onClose(), this);
			},
			'hidden.bs.modal':function(){
				$.proxy(options.onClosed(), this);
			},
		}).modal('show');

		tmpl.find('#onConfirm').on('click',function(){
			options.onConfirm(tmpl);
			tmpl.modal('hide');
		});

		tmpl.find('#onCancel').on('click',function(){
			options.onCancel(tmpl);
			tmpl.modal('hide');
		});
	};

	$.block = function(o){
		var defaults={
			backdrop:'static',
			keyboard:false,
			show:true,
			width:200
		};

		var options = $.extend(true, {}, defaults, o);

		var tmpl=['<div id="blockui" class="modal">',
				'<div class="progress progress-striped active" style="margin-bottom:0;">',
						'<div class="progress-bar" style="width: 100%;"></div>',
				'</div>',
		'</div>'].join('');

		tmpl=$(tmpl);

		tmpl.modal({
			backdrop:options.backdrop,
			keyboard:options.keyboard,
			show:options.show,
			width:options.width
		});
	};

	$.unblock=function(){
		if($('#blockui').length) $('#blockui').modal('hide');
	};

	$.nl2br = function(varTest){
		return varTest.replace(/(\r\n|\n\r|\r|\n)/g, "<br>");
	};
})(window.jQuery);
