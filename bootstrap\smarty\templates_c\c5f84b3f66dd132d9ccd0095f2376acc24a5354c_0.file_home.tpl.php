<?php
/* Smarty version 5.5.1, created on 2025-07-10 08:30:23
  from 'file:home.tpl' */

/* @var \Smarty\Template $_smarty_tpl */
if ($_smarty_tpl->getCompiled()->isFresh($_smarty_tpl, array (
  'version' => '5.5.1',
  'unifunc' => 'content_686f5dff479d51_37621365',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'c5f84b3f66dd132d9ccd0095f2376acc24a5354c' => 
    array (
      0 => 'home.tpl',
      1 => 1752129017,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
))) {
function content_686f5dff479d51_37621365 (\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates';
$_smarty_tpl->getInheritance()->init($_smarty_tpl, true);
?>



<?php 
$_smarty_tpl->getInheritance()->instanceBlock($_smarty_tpl, 'Block_2095567315686f5dff4640d5_88852689', "body");
?>

<?php $_smarty_tpl->getInheritance()->endChild($_smarty_tpl, "base.tpl", $_smarty_current_dir);
}
/* {block "body"} */
class Block_2095567315686f5dff4640d5_88852689 extends \Smarty\Runtime\Block
{
public function callBlock(\Smarty\Template $_smarty_tpl) {
$_smarty_current_dir = 'D:\\Projects\\Longwan\\HQ2\\templates';
?>

    <?php ob_start();
echo $_smarty_tpl->getSmarty()->getFunctionHandler('can')->handle(array('key'=>'ADMIN'), $_smarty_tpl);
$_prefixVariable1 = ob_get_clean();
if ($_prefixVariable1) {?>
        Can Admin
    <?php }?>

    <?php ob_start();
echo $_smarty_tpl->getSmarty()->getFunctionHandler('can')->handle(array('key'=>'SKU'), $_smarty_tpl);
$_prefixVariable2 = ob_get_clean();
if ($_prefixVariable2) {?>
        Can SKU
    <?php }?>

    <?php ob_start();
echo $_smarty_tpl->getSmarty()->getFunctionHandler('can')->handle(array('key'=>'LOGIN'), $_smarty_tpl);
$_prefixVariable3 = ob_get_clean();
if ($_prefixVariable3) {?>
        Can LOGIN
    <?php }?>

    <?php ob_start();
echo $_smarty_tpl->getSmarty()->getFunctionHandler('can')->handle(array('key'=>'LOGIN','branch_id'=>2), $_smarty_tpl);
$_prefixVariable4 = ob_get_clean();
if ($_prefixVariable4) {?>
        Can LOGIN
    <?php }?>

    <?php $_block_repeat=true;
if (!$_smarty_tpl->getSmarty()->getBlockHandler('access')) {
throw new \Smarty\Exception('block tag \'access\' not callable or registered');
}

echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"1"), null, $_smarty_tpl, $_block_repeat);
while ($_block_repeat) {
  ob_start();
?> 
        Access 1
    <?php $_block_repeat=false;
echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"1"), ob_get_clean(), $_smarty_tpl, $_block_repeat);
}
?>

    <?php $_block_repeat=true;
if (!$_smarty_tpl->getSmarty()->getBlockHandler('access')) {
throw new \Smarty\Exception('block tag \'access\' not callable or registered');
}

echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"2"), null, $_smarty_tpl, $_block_repeat);
while ($_block_repeat) {
  ob_start();
?> 
        Access 2
    <?php $_block_repeat=false;
echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"2"), ob_get_clean(), $_smarty_tpl, $_block_repeat);
}
?>

    <?php $_block_repeat=true;
if (!$_smarty_tpl->getSmarty()->getBlockHandler('access')) {
throw new \Smarty\Exception('block tag \'access\' not callable or registered');
}

echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"3"), null, $_smarty_tpl, $_block_repeat);
while ($_block_repeat) {
  ob_start();
?> 
        Access 3
    <?php $_block_repeat=false;
echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"3"), ob_get_clean(), $_smarty_tpl, $_block_repeat);
}
?>

    <?php $_block_repeat=true;
if (!$_smarty_tpl->getSmarty()->getBlockHandler('access')) {
throw new \Smarty\Exception('block tag \'access\' not callable or registered');
}

echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"4"), null, $_smarty_tpl, $_block_repeat);
while ($_block_repeat) {
  ob_start();
?> 
        Access 4
    <?php $_block_repeat=false;
echo $_smarty_tpl->getSmarty()->getBlockHandler('access')->handle(array('key'=>"4"), ob_get_clean(), $_smarty_tpl, $_block_repeat);
}
?>


    <?php ob_start();
echo $_smarty_tpl->getSmarty()->getFunctionHandler('config')->handle(array('key'=>'app.debug','default'=>false), $_smarty_tpl);
$_prefixVariable5 = ob_get_clean();
if ($_prefixVariable5) {?>
        Debug
    <?php }?>
        <?php
}
}
/* {/block "body"} */
}
