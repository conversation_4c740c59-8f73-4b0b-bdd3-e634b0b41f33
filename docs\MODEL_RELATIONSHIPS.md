# Model Relationships

This document explains the relationship functionality added to the models, providing Laravel-like Eloquent relationships.

## Overview

The models now support four types of relationships:
- **hasOne** - One-to-one relationship
- **hasMany** - One-to-many relationship  
- **belongsTo** - Inverse one-to-one/many relationship
- **belongsToMany** - Many-to-many relationship

## Relationship Types

### 1. hasOne (One-to-One)

Defines a one-to-one relationship where the current model has one related model.

```php
// In User model
public static function profile()
{
    return static::hasOne(UserProfile::class, 'user_id', 'id');
}

// Usage
$user = User::find(1);
$profile = $user->profile; // Returns UserProfile instance or null
```

**Parameters:**
- `$related` - Related model class
- `$foreignKey` - Foreign key on related table (default: `user_id`)
- `$localKey` - Local key on current table (default: `id`)

### 2. hasMany (One-to-Many)

Defines a one-to-many relationship where the current model has multiple related models.

```php
// In User model
public static function privileges()
{
    return static::hasMany(UserPrivilege::class, 'user_id', 'id');
}

// Usage
$user = User::find(1);
$privileges = $user->privileges; // Returns array of UserPrivilege instances
```

**Parameters:**
- `$related` - Related model class
- `$foreignKey` - Foreign key on related table (default: `user_id`)
- `$localKey` - Local key on current table (default: `id`)

### 3. belongsTo (Inverse Relationship)

Defines the inverse of a one-to-one or one-to-many relationship.

```php
// In UserProfile model
public static function user()
{
    return static::belongsTo(User::class, 'user_id', 'id');
}

// Usage
$profile = UserProfile::find(1);
$user = $profile->user; // Returns User instance or null
```

**Parameters:**
- `$related` - Related model class
- `$foreignKey` - Foreign key on current table (default: `user_id`)
- `$ownerKey` - Owner key on related table (default: `id`)

### 4. belongsToMany (Many-to-Many)

Defines a many-to-many relationship using a pivot table.

```php
// In User model
public static function branches()
{
    return static::belongsToMany(Branch::class, 'user_branch', 'user_id', 'branch_id');
}

// Usage
$user = User::find(1);
$branches = $user->branches; // Returns array of Branch instances with pivot data
```

**Parameters:**
- `$related` - Related model class
- `$table` - Pivot table name (default: alphabetically sorted model names)
- `$foreignPivotKey` - Foreign key of current model in pivot table
- `$relatedPivotKey` - Foreign key of related model in pivot table

## Usage Examples

### Basic Relationship Access

```php
// Get user and access relationships
$user = User::find(1);

// One-to-one
$profile = $user->profile;
echo $profile->bio;

// One-to-many
$privileges = $user->privileges;
foreach ($privileges as $privilege) {
    echo $privilege->privilege_id;
}

// Many-to-many
$branches = $user->branches;
foreach ($branches as $branch) {
    echo $branch->name;
    echo $branch->pivot['created_at']; // Access pivot data
}
```

### Relationship Methods

#### Loading Relationships

```php
$user = User::find(1);

// Explicit loading
$user->load('privileges');
$user->loadRelations(['privileges', 'branches']);

// Check if loaded
if ($user->relationLoaded('privileges')) {
    echo "Privileges are loaded";
}

// Get relationship instance
$relationship = $user->getRelationship('privileges');
```

#### Counting Related Models

```php
$user = User::find(1);

// Count related models
$privilegeCount = $user->getRelationship('privileges')->count();
$branchCount = $user->getRelationship('branches')->count();
```

#### Creating Related Models

```php
$user = User::find(1);

// Create related model (hasOne/hasMany)
$privilege = $user->getRelationship('privileges')->create([
    'privilege_id' => 5,
    'branch_id' => 1
]);
```

#### Many-to-Many Operations

```php
$user = User::find(1);
$branchRelation = $user->getRelationship('branches');

// Attach a branch
$branchRelation->attach(2); // Attach branch ID 2

// Attach with pivot data
$branchRelation->attach(3, ['role' => 'manager', 'created_at' => date('Y-m-d H:i:s')]);

// Detach a branch
$branchRelation->detach(2); // Detach branch ID 2

// Detach all branches
$branchRelation->detach();
```

#### BelongsTo Operations

```php
$profile = UserProfile::find(1);
$user = User::find(2);

// Associate models
$profileRelation = $profile->getRelationship('user');
$profileRelation->associate($user);
```

## Model Examples

### User Model with Relationships

```php
class User extends Model
{
    protected static $table = 'users';
    protected static $fillable = ['name', 'username', 'email'];

    // One-to-one
    public static function profile()
    {
        return static::hasOne(UserProfile::class, 'user_id', 'id');
    }

    // One-to-many
    public static function privileges()
    {
        return static::hasMany(UserPrivilege::class, 'user_id', 'id');
    }

    // Many-to-many
    public static function branches()
    {
        return static::belongsToMany(Branch::class, 'user_branch', 'user_id', 'branch_id');
    }
}
```

### UserProfile Model

```php
class UserProfile extends Model
{
    protected static $table = 'user_profiles';
    protected static $fillable = ['user_id', 'avatar', 'bio', 'phone'];

    // Inverse one-to-one
    public static function user()
    {
        return static::belongsTo(User::class, 'user_id', 'id');
    }
}
```

### Branch Model

```php
class Branch extends Model
{
    protected static $table = 'branches';
    protected static $fillable = ['code', 'description'];

    // Many-to-many (inverse)
    public static function users()
    {
        return static::belongsToMany(User::class, 'user_branch', 'branch_id', 'user_id');
    }

    // One-to-many
    public static function userPrivileges()
    {
        return static::hasMany(UserPrivilege::class, 'branch_id', 'id');
    }
}
```

## Database Schema

### Required Tables

For the examples above, you need these tables:

```sql
-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    username VARCHAR(255) UNIQUE,
    email VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- User profiles table (one-to-one)
CREATE TABLE user_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    avatar VARCHAR(255),
    bio TEXT,
    phone VARCHAR(50),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Branches table
CREATE TABLE branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50),
    description VARCHAR(255),
    active TINYINT DEFAULT 1,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- User privileges table (one-to-many)
CREATE TABLE user_privilege (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    privilege_id INT,
    branch_id INT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id)
);

-- User-Branch pivot table (many-to-many)
CREATE TABLE user_branch (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    branch_id INT,
    role VARCHAR(50),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    UNIQUE KEY unique_user_branch (user_id, branch_id)
);
```

## Advanced Features

### Lazy Loading

Relationships are loaded automatically when accessed:

```php
$user = User::find(1);
// No database query yet

$privileges = $user->privileges;
// Now the relationship query is executed
```

### Relationship Caching

Once loaded, relationships are cached on the model instance:

```php
$user = User::find(1);
$privileges1 = $user->privileges; // Database query
$privileges2 = $user->privileges; // Uses cached result
```

### Nested Relationships

Access related models through relationships:

```php
$user = User::find(1);
foreach ($user->privileges as $privilege) {
    echo $privilege->branch->name; // Access branch through privilege
}
```

## Performance Considerations

### Indexing

Ensure proper database indexes:

```sql
-- Foreign key indexes
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_privilege_user_id ON user_privilege(user_id);
CREATE INDEX idx_user_privilege_branch_id ON user_privilege(branch_id);
CREATE INDEX idx_user_branch_user_id ON user_branch(user_id);
CREATE INDEX idx_user_branch_branch_id ON user_branch(branch_id);
```

### N+1 Query Problem

Be aware of the N+1 query problem:

```php
// This will cause N+1 queries
$users = User::all();
foreach ($users as $user) {
    echo $user->profile->bio; // Each iteration causes a query
}

// Better approach: eager loading (future enhancement)
// $users = User::with('profile')->get();
```

## Testing

Test relationships using the provided test page:
- Visit `/test_relationships.php` in your browser
- See live examples of all relationship types
- Test with your actual database data
- Verify relationship loading and caching

## Error Handling

```php
try {
    $user = User::find(1);
    $privileges = $user->privileges;
} catch (Exception $e) {
    echo "Error loading relationship: " . $e->getMessage();
}

// Safe access
$user = User::find(1);
if ($user && $user->profile) {
    echo $user->profile->bio;
}
```

## Best Practices

1. **Define Inverse Relationships**: Always define both sides of relationships
2. **Use Proper Naming**: Follow Laravel conventions for method names
3. **Index Foreign Keys**: Ensure all foreign keys are indexed
4. **Handle Null Cases**: Check for null relationships before accessing
5. **Use Type Hints**: Document return types in PHPDoc comments

The relationship system provides a powerful, Laravel-like way to work with related data while maintaining clean, readable code.
