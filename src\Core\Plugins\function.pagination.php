<?php

function smarty_function_pagination(array $params, Smarty_Internal_Template $template)
{
	$configs=$template->tpl_vars['configs']->value;
	$tpl=$template->tpl_vars['template']->value;

	$total = (isset($params['total']))?$params['total']:0;
	if($total<=0) return ""; //return empty
	$page = ($params['page'] == 0 ? 1 : intval($params['page']));
	$adjacents = (isset($params['adjacents']))?$params['adjacents']:2;
	$id = (isset($params['id']))?"id='".$params['id']."'":"";
	$class= (isset($params['class']))?"class='".$params['class']."'":"";
	$url = (isset($params['url']))?$params["url"]:'?';
	$func = (isset($params['func']))?$params["func"]:'';

	$href=($func=='')?$url."&page=%%page%%":"javascript:".$func;

	$prev = $page - 1;
	$next = $page + 1;
	$lpm1 = $total - 1;

	$pagination = "";

	if($total > 1)
	{
		$pagination .= "<div ".$id." class='text-center'><ul ".$class.">";

		if ($page > 1){
			$pagination.= "<li><a href='".str_replace("%%page%%",1,$href)."'>First</a></li>";
			$pagination.= "<li><a href='".str_replace("%%page%%",$prev,$href)."'>Prev</a></li>";
		}else{
			$pagination.= "<li class='active'><a href='javascript:void(0);'>First</a></li>";
			$pagination.= "<li class='active'><a href='javascript:void(0);'>Prev</a></li>";
		}

		if ($total < 7 + ($adjacents * 2))
		{
			for ($counter = 1; $counter <= $total; $counter++)
			{
				if ($counter == $page)
					$pagination.= "<li class='active'><a href='javascript:void(0);'>$counter</a></li>";
				else
					$pagination.= "<li><a href='".str_replace("%%page%%",$counter,$href)."'>$counter</a></li>";
			}
		}
		elseif($total > 5 + ($adjacents * 2))
		{
			if($page < 1 + ($adjacents * 2))
			{
				for ($counter = 1; $counter < 4 + ($adjacents * 2); $counter++)
				{
					if ($counter == $page)
						$pagination.= "<li class='active'><a href='javascript:void(0);'>$counter</a></li>";
					else
						$pagination.= "<li><a href='".str_replace("%%page%%",$counter,$href)."'>$counter</a></li>";
				}
				$pagination.= "<li class='disabled'><a href='javascript:void(0);'>...</a></li>";
				$pagination.= "<li><a href='".str_replace("%%page%%",$lpm1,$href)."'>$lpm1</a></li>";
				$pagination.= "<li><a href='".str_replace("%%page%%",$total,$href)."'>$total</a></li>";
			}
			elseif($total - ($adjacents * 2) > $page && $page > ($adjacents * 2))
			{
				$pagination.= "<li><a href='".str_replace("%%page%%",1,$href)."'>1</a></li>";
				$pagination.= "<li><a href='".str_replace("%%page%%",2,$href)."'>2</a></li>";
				$pagination.= "<li class='disabled'><a href='javascript:void(0);'>...</a></li>";
				for ($counter = $page - $adjacents; $counter <= $page + $adjacents; $counter++)
				{
					if ($counter == $page)
						$pagination.= "<li class='active'><a href='javascript:void(0);'>$counter</a></li>";
					else
						$pagination.= "<li><a href='".str_replace("%%page%%",$counter,$href)."'>$counter</a></li>";
				}
				$pagination.= "<li class='disabled'><a href='javascript:void(0);'>...</a></li>";
				$pagination.= "<li><a href='".str_replace("%%page%%",$lpm1,$href)."'>$lpm1</a></li>";
				$pagination.= "<li><a href='".str_replace("%%page%%",$total,$href)."'>$total</a></li>";
			}
			else
			{
				$pagination.= "<li><a href='".str_replace("%%page%%",1,$href)."'>1</a></li>";
				$pagination.= "<li><a href='".str_replace("%%page%%",2,$href)."'>2</a></li>";
				$pagination.= "<li class='disabled'><a href='javascript:void(0);'>...</a></li>";
				for ($counter = $total - (2 + ($adjacents * 2)); $counter <= $total; $counter++)
				{
					if ($counter == $page)
						$pagination.= "<li class='active'><a href='javascript:void(0);'>$counter</a></li>";
					else
						$pagination.= "<li><a href='".str_replace("%%page%%",$counter,$href)."'>$counter</a></li>";
				}
			}
		}

		if ($page < $counter - 1){
			$pagination.= "<li><a href='".str_replace("%%page%%",$next,$href)."'>Next</a></li>";
			$pagination.= "<li><a href='".str_replace("%%page%%",$total,$href)."'>Last</a></li>";
		}else{
			$pagination.= "<li class='active'><a href='javascript:void(0);'>Next</a></li>";
			$pagination.= "<li class='active'><a href='javascript:void(0);'>Last</a></li>";
		}
		$pagination.= "</ul></div>";

		return $pagination;
	}
	else{
		return "<br/>";
	}
}

?>
