<?php

namespace App\Controllers;

use App\Controller\Controller;
use App\Services\DebugBarService;

/**
 * Debug Test Controller
 * 
 * Controller for testing debug bar functionality in development mode.
 * Only accessible when debug mode is enabled.
 */
class DebugTestController extends Controller
{
    /**
     * Debug test page
     * 
     * Displays a test page with various debug bar features demonstrated.
     */
    public function index()
    {
        // Only allow access in debug mode
        if (!config('app.debug', false)) {
            http_response_code(404);
            echo "404 Not Found";
            return;
        }
        
        // Test debug bar logging
        DebugBarService::log('Debug test page loaded', 'info');
        DebugBarService::log('This is a warning message', 'warning');
        DebugBarService::log('This is an error message', 'error');
        
        // Test database queries
        try {
            $users = DB()->table('users')->limit(5)->get();
            DebugBarService::log('Retrieved ' . count($users) . ' users from database', 'info');
        } catch (\Exception $e) {
            DebugBarService::log('Database query failed: ' . $e->getMessage(), 'error');
            $users = [];
        }
        
        // Test timing measurement
        $result = DebugBarService::measure('test_operation', function() {
            // Simulate some work
            usleep(100000); // 100ms
            return 'Operation completed';
        });
        
        DebugBarService::log('Measured operation result: ' . $result, 'info');
        
        // Test configuration data
        $appConfig = config('app');
        DebugBarService::log('App configuration loaded', 'info');
        
        // Prepare data for template
        $data = [
            'title' => 'Debug Bar Test Page',
            'users' => $users,
            'config' => $appConfig,
            'debug_enabled' => true,
            'test_data' => [
                'timestamp' => date('Y-m-d H:i:s'),
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
            ]
        ];
        
        $this->view('debug-test', $data);
    }
    
    /**
     * AJAX endpoint for testing debug bar with AJAX requests
     */
    public function ajax()
    {
        // Only allow access in debug mode
        if (!config('app.debug', false)) {
            http_response_code(404);
            echo json_encode(['error' => 'Not found']);
            return;
        }
        
        header('Content-Type: application/json');
        
        DebugBarService::log('AJAX request received', 'info');
        
        // Test database query
        try {
            $count = DB()->table('users')->count();
            DebugBarService::log('User count query executed: ' . $count, 'info');
            
            echo json_encode([
                'success' => true,
                'user_count' => $count,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            DebugBarService::log('AJAX database error: ' . $e->getMessage(), 'error');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
}
