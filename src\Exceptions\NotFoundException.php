<?php

namespace App\Exceptions;

/**
 * 404 Not Found Exception
 * 
 * Thrown when a requested resource cannot be found.
 */
class NotFoundException extends HttpException
{
    public function __construct(
        string $message = 'The requested resource could not be found.',
        array $errorData = [],
        array $headers = []
    ) {
        parent::__construct(404, $message, $errorData, $headers);
    }
}
