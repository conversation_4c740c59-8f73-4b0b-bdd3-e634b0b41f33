<?php

namespace App\Models;

use App\Core\Database;

/**
 * User Model
 *
 * Handles user-related database operations including authentication,
 * registration, and user data retrieval.
 */
class Vendor
{
    /** @var Database */
    private $db;

    /**
     * User constructor
     *
     * Initializes the database connection for user operations.
     */
    public function __construct()
    {
        $this->db = DB();
    }

    /**
     * Register a new user
     *
     * Creates a new user account with the provided credentials.
     * Password is hashed using MD5 (consider upgrading to bcrypt for security).
     *
     * @param string $name The user's full name
     * @param string $username The user's username (must be unique)
     * @param string $password The user's plain text password
     * @return bool True if registration successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public function register($name, $username, $password)
    {
        $hashedPassword = md5($password);

        return $this->db->table('vendors')->insert([
            'name' => $name,
            'username' => $username,
            'password' => $hashedPassword,
        ]);
    }

    /**
     * Authenticate user credentials
     *
     * Verifies user username and password against stored credentials.
     * Uses MD5 hashing for password comparison.
     *
     * @param string $username The user's username
     * @param string $password The user's plain text password
     * @return bool True if authentication successful, false otherwise
     * @throws \Exception If database operation fails
     */
    public function authenticate($username, $password)
    {
        $user = $this->db->table('vendors')->where('username', $username)->first();

        if ($user && md5($password) == $user['password']) {
            return $user;
        }

        return false;
    }

    /**
     * Get user by id
     *
     * Retrieves user data by id.
     *
     * @param string $id The user's id
     * @return array|false User data or false if not found
     * @throws \Exception If database operation fails
     */
    public function getById($id)
    {
        $user =  $this->db->table('vendors')
            ->select('vendors.*')
            ->where('vendors.id', $id)
            ->first();

        return $user;
    }
}
