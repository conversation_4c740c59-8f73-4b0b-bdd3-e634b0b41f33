# Database REPLACE INTO Functionality

This document explains the REPLACE INTO functionality added to the Database class for MySQL upsert operations.

## Overview

The Database class now supports MySQL's `REPLACE INTO` statement through three new methods:
- `replace()` - Replace a single row
- `replaceMany()` - Replace multiple rows in a single query
- `replaceManyWithTransaction()` - Replace multiple rows with transaction support and batching

## What is REPLACE INTO?

`REPLACE INTO` is a MySQL-specific statement that works like `INSERT`, but if an old row has the same value as a new row for a PRIMARY KEY or UNIQUE index, the old row is deleted before the new row is inserted.

### Key Characteristics:
- **Upsert Operation**: Insert if record doesn't exist, replace if it does
- **Requires Unique Key**: Must have PRIMARY KEY or UNIQUE index to determine duplicates
- **Atomic Operation**: Delete + Insert happens atomically
- **Auto-increment Behavior**: May increment AUTO_INCREMENT values even for replacements

## Methods

### 1. replace(array $data): bool

Replaces a single row using MySQL's REPLACE INTO statement.

**Parameters:**
- `$data` (array): Associative array of column => value pairs

**Returns:**
- `bool`: True if the operation was successful

**Example:**
```php
$userData = [
    'id' => 1,
    'username' => 'john_doe',
    'name' => 'John Doe Updated',
    'email' => '<EMAIL>'
];

$db = new Database();
$result = $db->table('users')->replace($userData);
```

### 2. replaceMany(array $data): bool

Replaces multiple rows using a single REPLACE INTO statement.

**Parameters:**
- `$data` (array): Array of associative arrays, each representing a row

**Returns:**
- `bool`: True if all rows were processed successfully

**Example:**
```php
$users = [
    ['id' => 1, 'username' => 'john', 'name' => 'John Updated'],
    ['id' => 2, 'username' => 'jane', 'name' => 'Jane Updated'],
    ['username' => 'bob', 'name' => 'Bob New'] // New user without ID
];

$db = new Database();
$result = $db->table('users')->replaceMany($users);
```

### 3. replaceManyWithTransaction(array $data, int $batchSize = 1000): bool

Replaces multiple rows within a transaction with batching support.

**Parameters:**
- `$data` (array): Array of associative arrays, each representing a row
- `$batchSize` (int): Number of rows to process per batch (default: 1000)

**Returns:**
- `bool`: True if all rows were processed successfully

**Example:**
```php
$largeUserData = []; // Array with thousands of user records

$db = new Database();
$result = $db->table('users')->replaceManyWithTransaction($largeUserData, 500);
```

## Use Cases

### 1. Data Synchronization
Perfect for synchronizing data from external sources:

```php
// Sync users from external API
$externalUsers = fetchUsersFromAPI();
$db->table('users')->replaceMany($externalUsers);
```

### 2. Configuration Updates
Update application settings or user preferences:

```php
$settings = [
    ['user_id' => 1, 'setting_key' => 'theme', 'setting_value' => 'dark'],
    ['user_id' => 1, 'setting_key' => 'language', 'setting_value' => 'en'],
    ['user_id' => 2, 'setting_key' => 'theme', 'setting_value' => 'light']
];

$db->table('user_settings')->replaceMany($settings);
```

### 3. Cache Table Updates
Refresh cached data efficiently:

```php
$cacheData = generateCacheData();
$db->table('cache_table')->replaceManyWithTransaction($cacheData);
```

### 4. Inventory Management
Update product quantities and information:

```php
$products = [
    ['sku' => 'PROD001', 'quantity' => 100, 'price' => 29.99],
    ['sku' => 'PROD002', 'quantity' => 50, 'price' => 19.99]
];

$db->table('products')->replaceMany($products);
```

## Requirements

### Database Requirements
- **MySQL/MariaDB**: REPLACE INTO is MySQL-specific
- **Unique Keys**: Table must have PRIMARY KEY or UNIQUE index
- **Transaction Support**: Required for transaction-based methods

### Data Requirements
- All rows must have the same column structure
- Data array cannot be empty
- Column names must match actual table columns

## Error Handling

All methods include comprehensive error handling:

```php
try {
    $db->table('users')->replaceMany($userData);
    echo "Replace operation successful!";
} catch (Exception $e) {
    echo "Replace failed: " . $e->getMessage();
}
```

### Common Exceptions

1. **Empty data array:**
   ```
   Data array cannot be empty for replaceMany()
   ```

2. **Inconsistent column structure:**
   ```
   All rows must have the same column structure. Row 2 has different columns.
   ```

3. **Database errors:**
   ```
   Database error: Table 'users' doesn't exist
   ```

## Performance Comparison

### Individual Replace Operations (Slow)
```php
// DON'T DO THIS for multiple rows
foreach ($users as $user) {
    $db->table('users')->replace($user);
}
```

### Batch Replace Operations (Fast)
```php
// Good: Single query for multiple rows
$db->table('users')->replaceMany($users);

// Best: Transaction with batching for large datasets
$db->table('users')->replaceManyWithTransaction($users, 1000);
```

## REPLACE vs INSERT vs UPDATE

| Operation | New Records | Existing Records | Use Case |
|-----------|-------------|------------------|----------|
| INSERT | ✅ Creates | ❌ Fails (duplicate key error) | Only new data |
| UPDATE | ❌ No effect | ✅ Updates | Only existing data |
| REPLACE | ✅ Creates | ✅ Replaces (delete + insert) | Mixed new/existing data |

## Model Integration Examples

### User Model Methods

```php
// Single user upsert
public function upsertUser(array $userData)
{
    $preparedData = [
        'username' => $userData['username'],
        'name' => $userData['name'],
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    return $this->db->table('users')->replace($preparedData);
}

// Bulk user synchronization
public function bulkSynchronizeUsers(array $users, int $batchSize = 500)
{
    // Data preparation and validation
    return $this->db->table('users')->replaceManyWithTransaction($users, $batchSize);
}
```

## Debug Bar Integration

All replace methods integrate with PHP Debug Bar:

- Query logging with execution details
- Data structure logging
- Error logging with detailed messages
- Transaction status tracking
- Batch progress monitoring

## Best Practices

1. **Use Appropriate Batch Sizes**: 500-1000 rows per batch for optimal performance
2. **Validate Data Structure**: Ensure consistent columns before replacement
3. **Handle Unique Constraints**: Design tables with appropriate unique keys
4. **Use Transactions for Large Datasets**: Provides rollback capability
5. **Monitor Auto-increment Values**: Be aware of potential gaps in sequences
6. **Consider Performance Impact**: REPLACE is slower than INSERT for new records

## Limitations and Considerations

### MySQL-Specific
- Only works with MySQL/MariaDB databases
- Not portable to other database systems

### Auto-increment Behavior
- May create gaps in AUTO_INCREMENT sequences
- Each REPLACE operation may increment the counter

### Performance Considerations
- REPLACE is slower than INSERT for new records
- Involves DELETE + INSERT operations internally
- May trigger additional index updates

## Migration Examples

### From Manual Upsert Logic
```php
// Before: Manual check and insert/update
$existing = $db->table('users')->where('username', $username)->first();
if ($existing) {
    $db->table('users')->where('id', $existing['id'])->update($userData);
} else {
    $db->table('users')->insert($userData);
}

// After: Simple replace operation
$db->table('users')->replace($userData);
```

### From Individual Operations
```php
// Before: Loop with individual operations
foreach ($users as $user) {
    // Complex upsert logic for each user
}

// After: Batch replace operation
$db->table('users')->replaceMany($users);
```

## Troubleshooting

### Issue: "Table doesn't have a unique key"
**Solution:** Add PRIMARY KEY or UNIQUE index to the table
```sql
ALTER TABLE users ADD UNIQUE KEY unique_username (username);
```

### Issue: Auto-increment gaps
**Solution:** This is expected behavior with REPLACE INTO. Consider using INSERT ... ON DUPLICATE KEY UPDATE if gaps are problematic.

### Issue: Performance slower than expected
**Solution:** 
- Use appropriate batch sizes
- Ensure proper indexing
- Consider INSERT ... ON DUPLICATE KEY UPDATE for better performance

## Compatibility

- **PHP Version**: 7.4+
- **Database**: MySQL 5.7+ or MariaDB 10.2+
- **PDO**: Required for database operations
- **Debug Bar**: Optional but recommended for monitoring
